package cn.iocoder.yudao.module.data.job;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.data.dal.dataobject.user.UserDO;
import cn.iocoder.yudao.module.data.dal.mysql.user.UserMapper;
import cn.iocoder.yudao.module.data.service.sync.DeptSyncInfoService;
import cn.iocoder.yudao.module.data.service.sync.UserSyncInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Component
public class UserJob {

    @Resource
    private DeptSyncInfoService deptSyncInfoService;
    @Resource
    private UserSyncInfoService userSyncInfoService;
    @Resource
    private UserMapper userMapper;


    @Scheduled(cron = "#{'${scheduled.tasks.userSync}'}")
    public void fixedDelayTask() {
        deptSyncInfoService.sync();

        int pageNo = 1;

        while (true) {
            PageResult<UserDO> userDOPageResult = userMapper.selectPage(pageNo, 10);
            if (userDOPageResult.getTotal() < 1000) {
                return;
            }
            List<UserDO> data = userDOPageResult.getList();
            if (CollectionUtils.isEmpty(data)) {
                break;
            }
            userSyncInfoService.sync(data);

            log.info("sync user page:{}", pageNo);

            pageNo++;
        }


    }
}
