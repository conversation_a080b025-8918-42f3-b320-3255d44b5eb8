package cn.iocoder.yudao.module.data.dal.dataobject.card;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 卡片数据 DO
 *
 * <AUTHOR>
 */
@TableName("pw_card")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardDO   {

    /**
     * 主键
     */
    @TableId
    private Long id;

    private Boolean deleted;
    /**
     * 关联用户
     */
    private String userId;
    /**
     * 卡号
     */
    private String cardNumber;
    /**
     * 卡密
     */
    private String cardPassword;
    /**
     * 卡类型
     *
     * 枚举 {@link TODO pw_card_type 对应的类}
     */
    private Integer cardType;
    /**
     * 挂失时间
     */
    private LocalDateTime hookDatetime;
    /**
     * 解挂时间
     */
    private LocalDateTime unhookDatetime;
    /**
     * 换卡时间
     */
    private LocalDateTime changeDatetime;
    /**
     * 卡片状态
     *
     * 枚举 {@link TODO pw_card_status 对应的类}
     */
    private Integer status;

    @Override
    public String toString() {
        return "CardDO{" +
                "id=" + id +
                ", deleted=" + deleted +
                ", userId=" + userId +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardPassword='" + cardPassword + '\'' +
                ", cardType=" + cardType +
                ", hookDatetime=" + hookDatetime +
                ", unhookDatetime=" + unhookDatetime +
                ", changeDatetime=" + changeDatetime +
                ", status=" + status +
                '}';
    }
}