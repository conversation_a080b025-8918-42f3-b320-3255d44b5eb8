package cn.iocoder.yudao.module.data.dal.dataobject.face;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 人脸数据 DO
 *
 * <AUTHOR>
 */
@TableName("pw_face")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FaceDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    private Boolean deleted;
    /**
     * 关联用户ID
     */
    private String userId;


    /**
     * 人脸信息
     */
    private String faceImage;

    @Override
    public String toString() {
        return "FaceDO{" +
                "id=" + id +
                ", deleted=" + deleted +
                ", userId=" + userId +
                ", faceImage='" + faceImage + '\'' +
                '}';
    }
}