package cn.iocoder.yudao.module.data.dal.mysql.sync;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.data.dal.dataobject.face.FaceDO;
import cn.iocoder.yudao.module.data.dal.dataobject.sync.DeptSyncInfoDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 卡片数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeptSyncInfoMapper extends BaseMapperX<DeptSyncInfoDO> {



}