package cn.iocoder.yudao.module.data.dal.dataobject.dept;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 部门表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("system_dept")
@Data
public class DeptDO {


    /**
     * 部门ID
     */
    @TableId
    private Long id;
    /**
     * 部门名称
     */
    private String name;
    /**
     * 标识
     */
    private String code;
    /**
     * 父部门ID
     * <p>
     * 关联 {@link #id}
     */
    private Long parentId;
    private String leaderUserId;
    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 联系电话
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 部门状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;


    private Boolean deleted;

    @Override
    public String toString() {
        return "DeptDO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", parentId=" + parentId +
                ", leaderUserId=" + leaderUserId +
                ", sort=" + sort +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", status=" + status +
                ", deleted=" + deleted +
                '}';
    }
}
