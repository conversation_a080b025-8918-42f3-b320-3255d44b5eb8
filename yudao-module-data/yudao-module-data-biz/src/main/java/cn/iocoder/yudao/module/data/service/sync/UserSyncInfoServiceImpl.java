package cn.iocoder.yudao.module.data.service.sync;

import cn.hutool.crypto.digest.DigestUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.data.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.module.data.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.data.dal.dataobject.face.FaceDO;
import cn.iocoder.yudao.module.data.dal.dataobject.sync.UserSyncInfoDO;
import cn.iocoder.yudao.module.data.dal.dataobject.user.UserDO;
import cn.iocoder.yudao.module.data.dal.mysql.card.CardMapper;
import cn.iocoder.yudao.module.data.dal.mysql.dept.DeptMapper;
import cn.iocoder.yudao.module.data.dal.mysql.face.FaceMapper;
import cn.iocoder.yudao.module.data.dal.mysql.sync.UserSyncInfoMapper;
import cn.iocoder.yudao.module.system.api.sync.UserSyncApi;
import cn.iocoder.yudao.module.system.api.sync.dto.CardDTO;
import cn.iocoder.yudao.module.system.api.sync.dto.UserInfoDTO;
import cn.iocoder.yudao.module.system.api.sync.dto.UserSyncReqDTO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserSyncInfoServiceImpl implements UserSyncInfoService {
    @Resource
    private UserSyncInfoMapper userSyncInfoMapper;

    @Resource
    private UserSyncApi userSyncAPI;


    @Resource
    private CardMapper cardMapper;
    @Resource
    private FaceMapper faceMapper;
    @Resource
    private DeptMapper deptMapper;


    @Override
    public void sync(List<UserDO> data) {
        syncUser(data);
    }

    private boolean existCard(List<CardDTO> cardDTOS, String cardNumber) {
        for (CardDTO cardDTO : cardDTOS) {
            if (StringUtils.equals(cardDTO.getCardNumber(), cardNumber)) {
                return true;
            }
        }

        return false;
    }

    private UserSyncReqDTO convert(UserDO userDO, List<FaceDO> faceDOS, List<CardDO> cardDOS, DeptDO deptDO) {
        UserSyncReqDTO userSyncReqDTO = new UserSyncReqDTO();
        userSyncReqDTO.setUsername(userDO.getUsername());
        UserInfoDTO userInfoDTO = convert(userDO, deptDO);
        userSyncReqDTO.setUserInfoDTO(userInfoDTO);

        String faceImage = userDO.getFaceImage();
        if (StringUtils.isEmpty(faceImage) && !CollectionUtils.isEmpty(faceDOS)) {
            Map<String, List<FaceDO>> faceDOMap = faceDOS.stream().collect(Collectors.groupingBy(FaceDO::getUserId));
            List<FaceDO> faceDOS1 = faceDOMap.get(userDO.getId().toString());
            if (!CollectionUtils.isEmpty(faceDOS1)) {
                for (FaceDO faceDO : faceDOS1) {
                    if (faceDO.getDeleted()) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(faceDO.getFaceImage())) {
                        faceImage = faceDO.getFaceImage();
                        break;
                    }
                }
            }
        }
        userSyncReqDTO.setFaceImage(faceImage);

        List<CardDTO> cardDTOList = new ArrayList<>();
        userSyncReqDTO.setCardDTOS(cardDTOList);

        if (!CollectionUtils.isEmpty(cardDOS)) {
            for (CardDO cardDO : cardDOS) {
                if (cardDO.getDeleted()) {
                    continue;
                }
                cardDTOList.add(convert(cardDO));
            }
        }

        if (StringUtils.isNotBlank(userDO.getCardNumber())) {
            if (!existCard(cardDTOList, userDO.getCardNumber())) {
                CardDTO cardDTO = new CardDTO();
                cardDTO.setCardNumber(userDO.getCardNumber());
                cardDTO.setCardPassword("");
                cardDTO.setCardType(1);
                cardDTO.setStatus(0);
                cardDTOList.add(cardDTO);
            } else {
                log.info("user card is more, user:{}", userDO.getId());
            }

        }


        return userSyncReqDTO;
    }

    private CardDTO convert(CardDO cardDO) {
        CardDTO cardDTO = new CardDTO();
        cardDTO.setCardNumber(cardDO.getCardNumber());
        cardDTO.setCardPassword(cardDO.getCardPassword());
        cardDTO.setCardType(cardDO.getCardType());
        cardDTO.setStatus(cardDO.getStatus());
        return cardDTO;
    }

    private UserInfoDTO convert(UserDO userDO, DeptDO deptDO) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUsername(userDO.getUsername());
        userInfoDTO.setDeleted(userDO.getDeleted());
        userInfoDTO.setNickname(userDO.getNickname());
        userInfoDTO.setEmail(userDO.getEmail());
        userInfoDTO.setMobile(userDO.getMobile());
        userInfoDTO.setSex(userDO.getSex());
        userInfoDTO.setSslmc(userDO.getSslmc());
        userInfoDTO.setStatus(userDO.getStatus());
        userInfoDTO.setCategory(userDO.getCategory());
        userInfoDTO.setRylb(userDO.getRylb());
        if (deptDO != null) {
            userInfoDTO.setDeptName(deptDO.getName());
        }

        return userInfoDTO;
    }

    private UserSyncInfoDO convert(UserDO user, UserSyncReqDTO userSyncReqDTO) {
        UserSyncInfoDO userSyncInfoDO = new UserSyncInfoDO();
        userSyncInfoDO.setUserId(user.getId());

        String hash = DigestUtil.md5Hex(userSyncReqDTO.getUserInfoDTO().toString());
        userSyncInfoDO.setInfoHash(hash);
        userSyncInfoDO.setFaceHash(DigestUtil.md5Hex(userSyncReqDTO.getFaceImage()));

        StringBuilder stringBuffer = new StringBuilder();
        List<CardDTO> cardDTOS = userSyncReqDTO.getCardDTOS();
        if (!CollectionUtils.isEmpty(cardDTOS)) {
            for (CardDTO cardDTO : cardDTOS) {
                stringBuffer.append(cardDTO.toString());
            }
        }
        String cardHash = DigestUtil.md5Hex(stringBuffer.toString());
        userSyncInfoDO.setCardHash(cardHash);


        return userSyncInfoDO;
    }

    private DeptDO find(List<DeptDO> all, Long deptId) {
        if (deptId == null) {
            return null;
        }
        for (DeptDO deptDO : all) {
            if (StringUtils.equals(deptDO.getCode(), deptId.toString())) {
                return deptDO;
            }
        }
        return null;
    }

    private void syncUser(List<UserDO> data) {
        List<Long> userIds = data.stream().map(UserDO::getId).toList();
        List<String> usernameList = data.stream().map(UserDO::getUsername).toList();
        List<UserSyncInfoDO> userSyncInfoDOS = userSyncInfoMapper.selectList(UserSyncInfoDO::getUserId, userIds);
        Map<Long, UserSyncInfoDO> userSyncInfoDOMap = userSyncInfoDOS.stream().collect(Collectors.toMap(UserSyncInfoDO::getUserId, s -> s));

        List<FaceDO> faceDOS = faceMapper.selectList(FaceDO::getUserId, usernameList);
        Map<String, List<FaceDO>> faceDOMap = faceDOS.stream().collect(Collectors.groupingBy(FaceDO::getUserId));
        List<CardDO> cardDOS = cardMapper.selectList(CardDO::getUserId, usernameList);
        Map<String, List<CardDO>> cardDOMap = cardDOS.stream().collect(Collectors.groupingBy(CardDO::getUserId));
        List<DeptDO> deptDOS = deptMapper.selectList();

        for (UserDO user : data) {

            UserSyncReqDTO userSyncReqDTO = convert(user, faceDOMap.get(user.getUsername()), cardDOMap.get(user.getUsername()), find(deptDOS, user.getDeptId()));
            if (CollectionUtils.isEmpty(userSyncReqDTO.getCardDTOS()) && StringUtils.isEmpty(userSyncReqDTO.getFaceImage())) {
                log.info("user not face or card, user:{}", user.getId());
                //人脸和一卡通号均为空的数据不需要同步
                continue;
            }
            UserSyncInfoDO userSyncInfoDO = convert(user, userSyncReqDTO);
            UserSyncInfoDO userSyncInfoDO4db = userSyncInfoDOMap.get(user.getId());

            if (userSyncInfoDO4db == null) {
                CommonResult<Boolean> result = userSyncAPI.send(userSyncReqDTO);
                if (result.getData() != null && result.getData()) {
                    userSyncInfoMapper.insert(userSyncInfoDO);
                } else {
                    log.error("sync dept is error, user:{}", user.getId());
                }


            } else {
                if (StringUtils.equals(userSyncInfoDO4db.getInfoHash(), userSyncInfoDO.getInfoHash())) {
                    userSyncReqDTO.setUserInfoDTO(null);
                }
                if (StringUtils.equals(userSyncInfoDO4db.getFaceHash(), userSyncInfoDO.getFaceHash())) {
                    userSyncReqDTO.setFaceImage(null);
                }
                if (StringUtils.equals(userSyncInfoDO4db.getCardHash(), userSyncInfoDO.getCardHash())) {
                    userSyncReqDTO.setCardDTOS(null);
                }
                if (userSyncReqDTO.getUserInfoDTO() != null || userSyncReqDTO.getFaceImage() != null || userSyncReqDTO.getCardDTOS() != null) {
                    userSyncInfoDO.setUserId(userSyncInfoDO4db.getUserId());
                    CommonResult<Boolean> result = userSyncAPI.send(userSyncReqDTO);
                    if (result.getData() != null && result.getData()) {
                        UpdateWrapper<UserSyncInfoDO> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.lambda().eq(UserSyncInfoDO::getUserId, userSyncInfoDO.getUserId());
                        userSyncInfoMapper.update(userSyncInfoDO, updateWrapper);
                    } else {
                        log.error("sync dept is error, update user:{}", user.getId());
                    }


                }

            }
        }
    }


}
