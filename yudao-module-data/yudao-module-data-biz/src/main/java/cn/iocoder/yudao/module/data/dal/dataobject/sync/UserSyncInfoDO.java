package cn.iocoder.yudao.module.data.dal.dataobject.sync;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.springframework.data.annotation.Id;

@TableName("user_sync_info")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSyncInfoDO {
    @Id
    private Long userId;


    private String infoHash;
    private String cardHash;
    private String faceHash;

}
