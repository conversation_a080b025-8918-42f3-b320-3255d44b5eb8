package cn.iocoder.yudao.module.data.dal.dataobject.user;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.system.enums.common.SexEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台的用户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_users", autoResultMap = true) // 由于 SQL Server 的 system_user 是关键字，所以使用 system_users
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;

    private Boolean deleted;
    /**
     * 用户账号
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 部门 ID
     */
    private Long deptId;

    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 用户性别
     *
     * 枚举类 {@link SexEnum}
     */
    private Integer sex;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 人脸
     */
    private String faceImage;

    @Schema(description = "宿舍楼名称")
    private String sslmc;

    @Schema(description = "过期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime expdate;

    @Schema(description = "身份")
    private String category;

    @Schema(description = "1教职工 2学生 3流动人员 金智t-user4099的人员 4其它 不在认证里的一卡通的")
    private String rylb;


    @Override
    public String toString() {
        return "UserDO{" +
                "id=" + id +
                ", deleted=" + deleted +
                ", username='" + username + '\'' +
                ", nickname='" + nickname + '\'' +
                ", deptId=" + deptId +
                ", email='" + email + '\'' +
                ", mobile='" + mobile + '\'' +
                ", sex=" + sex +
                ", avatar='" + avatar + '\'' +
                ", status=" + status +
                ", sslmc='" + sslmc + '\'' +
                ", expdate=" + expdate +
                ", category='" + category + '\'' +
                ", rylb='" + rylb + '\'' +
                '}';
    }
}
