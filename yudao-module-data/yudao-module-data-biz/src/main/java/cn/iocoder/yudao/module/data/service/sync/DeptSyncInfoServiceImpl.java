package cn.iocoder.yudao.module.data.service.sync;

import cn.hutool.crypto.digest.DigestUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.data.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.data.dal.dataobject.sync.DeptSyncInfoDO;
import cn.iocoder.yudao.module.data.dal.dataobject.sync.UserSyncInfoDO;
import cn.iocoder.yudao.module.data.dal.mysql.dept.DeptMapper;
import cn.iocoder.yudao.module.data.dal.mysql.sync.DeptSyncInfoMapper;
import cn.iocoder.yudao.module.system.api.sync.DeptSyncApi;
import cn.iocoder.yudao.module.system.api.sync.dto.DeptSyncReqDTO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeptSyncInfoServiceImpl implements DeptSyncInfoService {
    @Resource
    private DeptMapper deptMapper;
    @Resource
    private DeptSyncInfoMapper deptSyncInfoMapper;
    @Resource
    private DeptSyncApi deptSyncApi;

    @Override
    public void sync() {
        //查询所有部门
        List<DeptDO> deptDOS = deptMapper.selectList();
        if (CollectionUtils.isAnyEmpty(deptDOS)) {
            return;
        }
        Map<Long, DeptDO> deptDOMap = deptDOS.stream().collect(Collectors.toMap(DeptDO::getId, d -> d));
        List<DeptSyncInfoDO> deptSyncInfoDOS = deptSyncInfoMapper.selectList();
        Map<Long, DeptSyncInfoDO> deptSyncInfoDOMap = deptSyncInfoDOS.stream().collect(Collectors.toMap(DeptSyncInfoDO::getDeptId, s -> s));

        for (DeptDO deptDO : deptDOS) {
            DeptSyncInfoDO deptSyncInfoDO = deptSyncInfoDOMap.get(deptDO.getId());
            String hash = DigestUtil.md5Hex(deptDO.toString());

            if (deptSyncInfoDO != null && StringUtils.equals(deptSyncInfoDO.getInfoHash(), hash)) {
                continue;
            }

            DeptSyncReqDTO deptSyncReqDTO = convert(deptDO, deptDOMap);
            CommonResult<Boolean> result = deptSyncApi.send(deptSyncReqDTO);
            if (result.getData() != null && result.getData()) {
                DeptSyncInfoDO deptSyncInfoDO1 = new DeptSyncInfoDO();
                deptSyncInfoDO1.setDeptId(deptDO.getId());
                deptSyncInfoDO1.setInfoHash(hash);
                if (deptSyncInfoDO == null) {
                    deptSyncInfoMapper.insert(deptSyncInfoDO1);
                } else {
                    UpdateWrapper<DeptSyncInfoDO> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().eq(DeptSyncInfoDO::getDeptId, deptSyncInfoDO1.getDeptId());
                    deptSyncInfoMapper.update(deptSyncInfoDO1,updateWrapper);
                }
            }else{
                log.error("sync dept is error, dept:{}",deptSyncReqDTO);
            }

        }

    }

    private DeptSyncReqDTO convert(DeptDO deptDO, Map<Long, DeptDO> deptDOMap) {
        DeptSyncReqDTO deptSyncReqDTO = new DeptSyncReqDTO();
        deptSyncReqDTO.setName(deptDO.getName());
        deptSyncReqDTO.setCode(deptDO.getCode());
        DeptDO parent = deptDOMap.get(deptDO.getParentId());
        if (parent != null) {
            deptSyncReqDTO.setParentName(parent.getName());
        }
        deptSyncReqDTO.setSort(deptDO.getSort());
        deptSyncReqDTO.setPhone(deptDO.getPhone());
        deptSyncReqDTO.setEmail(deptDO.getEmail());
        deptSyncReqDTO.setStatus(deptDO.getStatus());
        deptSyncReqDTO.setDeleted(deptDO.getDeleted());
        return deptSyncReqDTO;
    }
}
