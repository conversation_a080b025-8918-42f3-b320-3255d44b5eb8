package cn.iocoder.yudao.module.data.framework.rpc.config;

import cn.iocoder.yudao.module.system.api.sync.DeptSyncApi;
import cn.iocoder.yudao.module.system.api.sync.UserSyncApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {DeptSyncApi.class, UserSyncApi.class})
public class RpcConfiguration {
}
