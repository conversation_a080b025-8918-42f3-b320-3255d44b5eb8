package cn.iocoder.yudao.module.data.dal.mysql.user;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.data.dal.dataobject.user.UserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 卡片数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapperX<UserDO> {
    default PageResult<UserDO> selectPage(Integer pageNo,Integer pageSize) {
        LambdaQueryWrapperX<UserDO> queryWrapper = new LambdaQueryWrapperX<>();
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(pageNo);
        pageParam.setPageSize(pageSize);
        return selectPage(pageParam, queryWrapper);
    }


}