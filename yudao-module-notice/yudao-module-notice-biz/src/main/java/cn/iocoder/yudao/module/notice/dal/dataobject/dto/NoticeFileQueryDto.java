package cn.iocoder.yudao.module.notice.dal.dataobject.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 班牌通知公告主对象 notice_announcement
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
public class NoticeFileQueryDto extends PageParam implements Serializable
{

    // 文件名
    private String fileName;


    private String caption;

}
