package cn.iocoder.yudao.module.notice.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.notice.dal.dataobject.NoticeConfig;
import cn.iocoder.yudao.module.notice.dal.dataobject.dto.*;
import cn.iocoder.yudao.module.notice.dal.dataobject.vo.NoticeAnnouncementImageVo;
import cn.iocoder.yudao.module.notice.dal.dataobject.vo.NoticeAnnouncementVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.iocoder.yudao.module.notice.service.INoticeAnnouncementService;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 班牌通知公告主Controller
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Tag(name = "班牌通知公告 - 公告管理")
@RestController
@RequestMapping("/notice/announcement")
public class NoticeAnnouncementController
{
    @Autowired
    private INoticeAnnouncementService noticeAnnouncementService;


    // 创建公告
    @PostMapping("/addNoticeAnnouncement")
    @Operation(summary = "创建公告")
    public CommonResult<Long> addNoticeAnnouncement(@Valid @RequestBody NoticeAnnouncementDto noticeAnnouncement) {
        Long id = noticeAnnouncementService.addNoticeAnnouncement(noticeAnnouncement);
        return success(id);
    }

    // 编辑公告
    @PutMapping("/updateNoticeAnnouncement")
    @Operation(summary = "编辑公告")
    public CommonResult<Long> updateNoticeAnnouncement(@Valid @RequestBody NoticeAnnouncementDto noticeAnnouncement) {
        Long id = noticeAnnouncementService.updateNoticeAnnouncement(noticeAnnouncement);
        return success(id);
    }

    // 删除公告
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除公告")
    @Parameter(name = "id", description = "公告id", required = true, example = "1024")
    public CommonResult<Boolean> deleteNoticeAnnouncement(@PathVariable("id") Long id) {
        noticeAnnouncementService.deleteNoticeAnnouncement(id);
        return success(true);
    }

    // 查询公告列表
    @GetMapping("/selectPageList")
    @Operation(summary = "/查询公告列表")
    public CommonResult<PageResult<NoticeAnnouncementVo>> selectPageList(@Valid NoticeAnnouncementQueryDto noticeAnnouncementQueryDto) {
        return CommonResult.success(noticeAnnouncementService.selectPageList(noticeAnnouncementQueryDto));
    }



    // 查询文件列表
    @GetMapping("/selectFilePageList")
    @Operation(summary = "/查询文件列表")
    public CommonResult<PageResult<NoticeAnnouncementImageVo>> selectFilePageList(@Valid NoticeFileQueryDto noticeFileQueryDto) {
        return CommonResult.success(noticeAnnouncementService.selectFilePageList(noticeFileQueryDto));
    }


    // 根据id获取上传的公告详情
    @GetMapping("/selectNoticeAnnouncementById/{id}")
    @Operation(summary = "/根据id获取上传的公告详情")
    public CommonResult<NoticeAnnouncementVo> selectNoticeAnnouncementById(@PathVariable("id") Long id) {
        return CommonResult.success(noticeAnnouncementService.selectNoticeAnnouncementById(id));
    }

    // 播放视频的文件流
    @Operation(summary = "播放视频的文件流")
    @PermitAll
    @GetMapping("/getVideoById/{id}")
    public void getVideoById(@PathVariable("id") Long id, HttpServletRequest request, HttpServletResponse response) {
        noticeAnnouncementService.getVideoById(id,request,response);

    }


    // 上传文件，判断文件类型来控制写入图片还是视频
    @Operation(summary ="上传文件，判断文件类型来控制写入图片还是视频")
    @PostMapping("/addNoticeAnnouncementFile")
    public CommonResult<String> addNoticeAnnouncementFile(@RequestBody NoticeAnnouncementFileDto noticeAnnouncementFileDto) {
        noticeAnnouncementService.addNoticeAnnouncementFile(noticeAnnouncementFileDto);
        return CommonResult.success("ok");
    }


    // 上传文件，判断文件类型来控制写入图片还是视频
    @Operation(summary ="上传文件，判断文件类型来控制写入图片还是视频")
    @PostMapping("/updateNoticeAnnouncementFile")
    public CommonResult<String> updateNoticeAnnouncementFile(@RequestBody NoticeAnnouncementFileDto noticeAnnouncementFileDto) {
        noticeAnnouncementService.updateNoticeAnnouncementFile(noticeAnnouncementFileDto);
        return CommonResult.success("ok");
    }


    // 修改轮播图片，主要修改轮播顺序
    @PutMapping("/updateNoticeAnnouncementImage")
    @Operation(summary = "修改轮播图片，主要修改轮播顺序")
    public CommonResult<Long> updateNoticeAnnouncementImage(@Valid @RequestBody NoticeAnnouncementImageDto noticeAnnouncement) {
        Long id = noticeAnnouncementService.updateNoticeAnnouncementImage(noticeAnnouncement);
        return success(id);
    }


    // 删除轮播图
    @DeleteMapping("/deleteNoticeAnnouncementImage/{id}")
    @Operation(summary = "删除轮播图")
    public CommonResult<Boolean> deleteNoticeAnnouncementImage(@PathVariable("id") Long id) {
        noticeAnnouncementService.deleteNoticeAnnouncementImage(id);
        return success(true);
    }

    // 删除视频
    @DeleteMapping("/deleteNoticeAnnouncementVideo/{id}")
    @Operation(summary = "删除视频")
    public CommonResult<Boolean> deleteNoticeAnnouncementVideo(@PathVariable("id") Long id) {
        noticeAnnouncementService.deleteNoticeAnnouncementVideo(id);
        return success(true);
    }


    // 获取最新的公告信息
    @GetMapping("/selectNewNoticeAnnouncement")
    @Operation(summary = "获取最新的公告信息")
    public CommonResult<NoticeAnnouncementVo> selectNewNoticeAnnouncement() {
        return CommonResult.success(noticeAnnouncementService.selectNewNoticeAnnouncement());
    }



    // 获取展示配置
    @GetMapping("/getNoticeConfig")
    @Operation(summary = "获取展示配置")
    public CommonResult<NoticeConfig> getNoticeConfig() {
        return CommonResult.success(noticeAnnouncementService.getNoticeConfig());
    }



    // 修改配置
    @PutMapping("/updateNoticeConfig")
    @Operation(summary = "修改配置")
    public CommonResult<String> updateNoticeConfig( @RequestBody NoticeConfig noticeConfig) {
        boolean bo = noticeAnnouncementService.updateNoticeConfig(noticeConfig);
        if (bo){
            return success("修改成功");
        }else {
            return error(500,"修改失败");
        }
    }

}
