package cn.iocoder.yudao.module.notice.service;

import java.util.List;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.notice.dal.dataobject.NoticeConfig;
import cn.iocoder.yudao.module.notice.dal.dataobject.dto.*;
import cn.iocoder.yudao.module.notice.dal.dataobject.vo.NoticeAnnouncementImageVo;
import cn.iocoder.yudao.module.notice.dal.dataobject.vo.NoticeAnnouncementVo;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.iocoder.yudao.module.notice.dal.dataobject.NoticeAnnouncement;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 班牌通知公告主Service接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface INoticeAnnouncementService extends IService<NoticeAnnouncement> {

    // 创建公告
    Long addNoticeAnnouncement(NoticeAnnouncementDto noticeAnnouncement);

    // 编辑公告
    Long updateNoticeAnnouncement(NoticeAnnouncementDto noticeAnnouncement);

    // 删除公告
    Long deleteNoticeAnnouncement(Long id);

    // 查询公告列表
    PageResult<NoticeAnnouncementVo> selectPageList(NoticeAnnouncementQueryDto noticeAnnouncementQueryDto);

    // 获取文件列表
    PageResult<NoticeAnnouncementImageVo> selectFilePageList(NoticeFileQueryDto noticeFileQueryDto);

    // 根据id获取公告详情
    NoticeAnnouncementVo selectNoticeAnnouncementById(Long id);

    // 获取最新的公告
    NoticeAnnouncementVo selectNewNoticeAnnouncement();


    // 修改轮播图片，主要修改轮播顺序
    Long updateNoticeAnnouncementImage(NoticeAnnouncementImageDto noticeAnnouncementDto);

    // 删除轮播图
    Long deleteNoticeAnnouncementImage(Long id);

    // 删除视频
    Long deleteNoticeAnnouncementVideo(Long id);

    // 上传文件，判断文件类型来控制写入图片还是视频
    void addNoticeAnnouncementFile(NoticeAnnouncementFileDto noticeAnnouncementFileDto);

    // 修改文件，判断文件类型来控制写入图片还是视频
    void updateNoticeAnnouncementFile(NoticeAnnouncementFileDto noticeAnnouncementFileDto);




    // 播放视频的文件流
    void getVideoById(Long id, HttpServletRequest request, HttpServletResponse response);

    NoticeConfig getNoticeConfig();

    boolean updateNoticeConfig(NoticeConfig noticeConfig);

}
