package cn.iocoder.yudao.module.notice.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 班牌通知公告主对象 notice_announcement
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
@NoArgsConstructor
@Accessors(chain = true)
@TableName("notice_config")
public class NoticeConfig
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id")
    private Long id;


    /** 显示类型，0轮播图，1视频 */
    @Schema(description = "显示类型，0轮播图，1视频")
    private String displayType;

    /** 是否全屏显示，0是1否 */
    @Schema(description = "是否全屏显示，0是1否 ")
    private String displayIsFull;



}
