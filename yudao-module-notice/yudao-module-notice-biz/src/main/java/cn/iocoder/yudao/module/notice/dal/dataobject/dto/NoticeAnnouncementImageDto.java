package cn.iocoder.yudao.module.notice.dal.dataobject.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 班牌通知公告图片轮播对象 notice_announcement_image
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
public class NoticeAnnouncementImageDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键ID，雪花算法生成 */
    private Long id;

    @Schema(description = "修改的文件类型，区分图片还是视频，0图片，1视频")
    private String type;

    /** 公告ID，逻辑关联announcement表 */
    private Long announcementId;

    /** 图片地址 */
    private String imageUrl;

    /** 图片注解 */
    private String caption;

    /** 排序号，越小越靠前 */
    private Long sortOrder;

    /** 状态，0启用，1禁用 */
    @Schema(description = "状态，0启用，1禁用")
    private String status;

    private Date createTime;

    private Date updateTime;
}
