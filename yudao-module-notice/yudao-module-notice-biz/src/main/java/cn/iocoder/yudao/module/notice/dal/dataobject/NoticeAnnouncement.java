package cn.iocoder.yudao.module.notice.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 班牌通知公告主对象 notice_announcement
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
@NoArgsConstructor
@Accessors(chain = true)
@TableName("notice_announcement")
public class NoticeAnnouncement
{
    private static final long serialVersionUID = 1L;

    /** 主键ID，雪花算法生成 */
    @TableId(type= IdType.INPUT)
    private Long id;

    /** 公告标题 */
    private String title;

    /** 富文本内容 */
    private String content;

    /** 展示类型：1-图片轮播，2-视频 */
    private Long type;

    /** 状态：0-草稿，1-发布 */
    private Long status;

    private Date createTime;

    private Date updateTime;

}
