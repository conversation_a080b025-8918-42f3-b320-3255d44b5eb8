package cn.iocoder.yudao.module.notice.service.impl;

import ch.qos.logback.core.util.StringUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.notice.dal.dataobject.NoticeAnnouncementImage;
import cn.iocoder.yudao.module.notice.dal.dataobject.NoticeAnnouncementVideo;
import cn.iocoder.yudao.module.notice.dal.dataobject.NoticeConfig;
import cn.iocoder.yudao.module.notice.dal.dataobject.dto.*;
import cn.iocoder.yudao.module.notice.dal.dataobject.vo.NoticeAnnouncementImageVo;
import cn.iocoder.yudao.module.notice.dal.dataobject.vo.NoticeAnnouncementVideoVo;
import cn.iocoder.yudao.module.notice.dal.dataobject.vo.NoticeAnnouncementVo;
import cn.iocoder.yudao.module.notice.dal.mysql.NoticeAnnouncementImageMapper;
import cn.iocoder.yudao.module.notice.dal.mysql.NoticeAnnouncementVideoMapper;
import cn.iocoder.yudao.module.notice.dal.mysql.NoticeConfigMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jcodec.api.FrameGrab;
import org.jcodec.api.JCodecException;
import org.jcodec.common.io.NIOUtils;
import org.jcodec.common.model.Picture;
import org.jcodec.scale.AWTUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import cn.iocoder.yudao.module.notice.dal.mysql.NoticeAnnouncementMapper;
import cn.iocoder.yudao.module.notice.dal.dataobject.NoticeAnnouncement;
import cn.iocoder.yudao.module.notice.service.INoticeAnnouncementService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;

/**
 * 班牌通知公告主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class NoticeAnnouncementServiceImpl extends ServiceImpl<NoticeAnnouncementMapper, NoticeAnnouncement> implements INoticeAnnouncementService {

    @Autowired
    private NoticeAnnouncementImageMapper noticeAnnouncementImageMapper;

    @Autowired
    private NoticeAnnouncementVideoMapper noticeAnnouncementVideoMapper;

    @Autowired
    private NoticeConfigMapper noticeConfigMapper;


    @Override
    public Long addNoticeAnnouncement(NoticeAnnouncementDto noticeAnnouncement) {
        NoticeAnnouncement announcement = BeanUtil.copyProperties(noticeAnnouncement, NoticeAnnouncement.class);
        announcement.setId(IdUtil.getSnowflakeNextId());
        announcement.setCreateTime(new Date());
        baseMapper.insert(announcement);
        return announcement.getId();
    }

    @Override
    public Long updateNoticeAnnouncement(NoticeAnnouncementDto noticeAnnouncement) {
        NoticeAnnouncement announcement = BeanUtil.copyProperties(noticeAnnouncement, NoticeAnnouncement.class);
        announcement.setUpdateTime(new Date());
        baseMapper.updateById(announcement);
        return noticeAnnouncement.getId();
    }

    @Override
    public Long deleteNoticeAnnouncement(Long id) {
        baseMapper.deleteById(id);
        return id;
    }

    @Override
    public PageResult<NoticeAnnouncementVo> selectPageList(NoticeAnnouncementQueryDto noticeAnnouncementQueryDto) {
        LambdaQueryWrapper<NoticeAnnouncement> noticeAnnouncementLambdaQueryWrapper = buildQueryWrapper(noticeAnnouncementQueryDto);
        Page<NoticeAnnouncement> page = new Page<>(noticeAnnouncementQueryDto.getPageNo(), noticeAnnouncementQueryDto.getPageSize());
        Page<NoticeAnnouncement> announcementPage = baseMapper.selectPage(page, noticeAnnouncementLambdaQueryWrapper);
        if (announcementPage.getTotal() == 0) {
            return PageResult.empty();
        }
        List<NoticeAnnouncementVo> noticeAnnouncementVos = BeanUtil.copyToList(announcementPage.getRecords(), NoticeAnnouncementVo.class);
        return new PageResult<>(noticeAnnouncementVos, page.getTotal());
    }


    @Override
    public PageResult<NoticeAnnouncementImageVo> selectFilePageList(NoticeFileQueryDto noticeFileQueryDto) {
        LambdaQueryWrapper<NoticeAnnouncementImage> noticeAnnouncementLambdaQueryWrapper = new LambdaQueryWrapper<>();
        noticeAnnouncementLambdaQueryWrapper.like(StringUtil.notNullNorEmpty(noticeFileQueryDto.getFileName()),
                NoticeAnnouncementImage::getFileName, noticeFileQueryDto.getFileName());
        noticeAnnouncementLambdaQueryWrapper.like(StringUtil.notNullNorEmpty(noticeFileQueryDto.getCaption()),
                NoticeAnnouncementImage::getCaption, noticeFileQueryDto.getCaption());
        Page<NoticeAnnouncementImage> page = new Page<>(noticeFileQueryDto.getPageNo(), noticeFileQueryDto.getPageSize());
        Page<NoticeAnnouncementImage> announcementPage = noticeAnnouncementImageMapper.selectPage(page, noticeAnnouncementLambdaQueryWrapper);
        if (announcementPage.getTotal() == 0) {
            return PageResult.empty();
        }
        List<NoticeAnnouncementImageVo> noticeAnnouncementVos = BeanUtil.copyToList(announcementPage.getRecords(), NoticeAnnouncementImageVo.class);
        return new PageResult<>(noticeAnnouncementVos, page.getTotal());
    }


    @Override
    public NoticeAnnouncementVo selectNoticeAnnouncementById(Long id) {
        NoticeAnnouncementVo noticeAnnouncementVo = BeanUtil.copyProperties(baseMapper.selectById(id), NoticeAnnouncementVo.class);
//        List<NoticeAnnouncementImage> noticeAnnouncementImages = noticeAnnouncementImageMapper
//                .selectList(new LambdaQueryWrapper<NoticeAnnouncementImage>()
//                        .eq(NoticeAnnouncementImage::getAnnouncementId, noticeAnnouncementVo.getId()));
//        List<NoticeAnnouncementVideo> noticeAnnouncementVideos = noticeAnnouncementVideoMapper.selectList(new LambdaQueryWrapper<NoticeAnnouncementVideo>()
//                .eq(NoticeAnnouncementVideo::getAnnouncementId, noticeAnnouncementVo.getId()).orderByDesc(NoticeAnnouncementVideo::getCreateTime));
//        noticeAnnouncementVo.setNoticeAnnouncementImageVosList(BeanUtil.copyToList(noticeAnnouncementImages, NoticeAnnouncementImageVo.class));
//        noticeAnnouncementVo.setNoticeAnnouncementVideoVosList(BeanUtil.copyToList(noticeAnnouncementVideos, NoticeAnnouncementVideoVo.class));
        return noticeAnnouncementVo;
    }

    @Override
    public NoticeAnnouncementVo selectNewNoticeAnnouncement() {
//        Page<NoticeAnnouncement> page = new Page<>(1, 1);
//        LambdaQueryWrapper<NoticeAnnouncement> lqw = Wrappers.lambdaQuery();
//        lqw.eq(NoticeAnnouncement::getStatus, 1);
//        lqw.orderByDesc(NoticeAnnouncement::getCreateTime);
//        Page<NoticeAnnouncement> announcementPage = baseMapper.selectPage(page, lqw);
//        if (announcementPage.getTotal() == 0) {
//            return new NoticeAnnouncementVo();
//        }
//        return selectNoticeAnnouncementById(announcementPage.getRecords().get(0).getId());

        NoticeAnnouncementVo noticeAnnouncementVo = new NoticeAnnouncementVo();
        noticeAnnouncementVo.setId(1L);
        List<NoticeAnnouncementImage> noticeAnnouncementImages = noticeAnnouncementImageMapper
                .selectList(new LambdaQueryWrapper<NoticeAnnouncementImage>()
                        .eq(NoticeAnnouncementImage::getStatus, "0")
                        .eq(NoticeAnnouncementImage::getFileType, "0")
                        .orderByAsc(NoticeAnnouncementImage::getSortOrder)
                );

        List<NoticeAnnouncementImage> noticeAnnouncementVideos = noticeAnnouncementImageMapper
                .selectList(new LambdaQueryWrapper<NoticeAnnouncementImage>()
                        .eq(NoticeAnnouncementImage::getStatus, "0")
                        .eq(NoticeAnnouncementImage::getFileType, "1")
                        .orderByDesc(NoticeAnnouncementImage::getCreateTime));

        noticeAnnouncementVo.setNoticeAnnouncementImageVosList(BeanUtil.copyToList(noticeAnnouncementImages, NoticeAnnouncementImageVo.class));
        noticeAnnouncementVo.setNoticeAnnouncementVideoVosList(BeanUtil.copyToList(noticeAnnouncementVideos, NoticeAnnouncementImageVo.class));
        return noticeAnnouncementVo;


    }

    @Override
    public Long updateNoticeAnnouncementImage(NoticeAnnouncementImageDto noticeAnnouncementDto) {
        if (noticeAnnouncementDto.getType().equals("0")) {
            NoticeAnnouncementImage noticeAnnouncementImage = new NoticeAnnouncementImage();
            noticeAnnouncementImage.setId(noticeAnnouncementDto.getId());
            noticeAnnouncementImage.setSortOrder(noticeAnnouncementDto.getSortOrder());
            noticeAnnouncementImage.setCaption(noticeAnnouncementDto.getCaption());
            noticeAnnouncementImage.setUpdateTime(new Date());
            noticeAnnouncementImageMapper.updateById(noticeAnnouncementImage);
        } else if (noticeAnnouncementDto.getType().equals("1")) {
            NoticeAnnouncementVideo noticeAnnouncementVideo = new NoticeAnnouncementVideo();
            noticeAnnouncementVideo.setId(noticeAnnouncementDto.getId());
            noticeAnnouncementVideo.setStatus(noticeAnnouncementDto.getStatus());
            noticeAnnouncementVideoMapper.updateById(noticeAnnouncementVideo);
        }
        return noticeAnnouncementDto.getId();
    }

    @Override
    public Long deleteNoticeAnnouncementImage(Long id) {
        noticeAnnouncementImageMapper.deleteById(id);
        return id;
    }

    @Override
    public Long deleteNoticeAnnouncementVideo(Long id) {
        noticeAnnouncementVideoMapper.deleteById(id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void addNoticeAnnouncementFile(NoticeAnnouncementFileDto noticeAnnouncementFileDto) {
        NoticeAnnouncementImage noticeAnnouncementImage = new NoticeAnnouncementImage();
        noticeAnnouncementImage.setId(IdUtil.getSnowflakeNextId());
        noticeAnnouncementImage.setAnnouncementId(1L);
        noticeAnnouncementImage.setSortOrder(noticeAnnouncementFileDto.getSortOrder());
        noticeAnnouncementImage.setCaption(noticeAnnouncementFileDto.getCaption());
        noticeAnnouncementImage.setCreateTime(new Date());
        noticeAnnouncementImage.setStatus(noticeAnnouncementFileDto.getStatus());
        noticeAnnouncementImage.setFileType(noticeAnnouncementFileDto.getFileType());
        noticeAnnouncementImage.setImageUrl(noticeAnnouncementFileDto.getFileUrl());
        noticeAnnouncementImage.setFileUrl(noticeAnnouncementFileDto.getFileUrl());
        noticeAnnouncementImageMapper.insert(noticeAnnouncementImage);
    }


    @Override
    public synchronized void updateNoticeAnnouncementFile(NoticeAnnouncementFileDto noticeAnnouncementFileDto) {
        NoticeAnnouncementImage noticeAnnouncementImage = new NoticeAnnouncementImage();
        noticeAnnouncementImage.setId(noticeAnnouncementFileDto.getId());
        noticeAnnouncementImage.setAnnouncementId(1L);
        noticeAnnouncementImage.setSortOrder(noticeAnnouncementFileDto.getSortOrder());
        noticeAnnouncementImage.setCaption(noticeAnnouncementFileDto.getCaption());
        noticeAnnouncementImage.setCreateTime(new Date());
        noticeAnnouncementImage.setStatus(noticeAnnouncementFileDto.getStatus());
        noticeAnnouncementImage.setFileType(noticeAnnouncementFileDto.getFileType());
        noticeAnnouncementImage.setImageUrl(noticeAnnouncementFileDto.getFileUrl());
        noticeAnnouncementImage.setFileUrl(noticeAnnouncementFileDto.getFileUrl());
        noticeAnnouncementImageMapper.updateById(noticeAnnouncementImage);
    }

    /**
     * 用JCodec提取视频首帧并转为Base64字符串
     *
     * @param tempFile MultipartFile对象
     * @return Base64字符串（不带前缀），失败返回null
     */
    public static String getFirstFrameBase64(File tempFile) {
        try {
            // 2. 用JCodec提取首帧
            FrameGrab grab = FrameGrab.createFrameGrab(NIOUtils.readableChannel(tempFile));
            Picture picture = grab.getNativeFrame();
            if (picture == null) {
                return null;
            }
            BufferedImage bufferedImage = AWTUtil.toBufferedImage(picture);
            // 3. 转为Base64
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "jpg", baos);
            return Base64.getEncoder().encodeToString(baos.toByteArray());
        } catch (IOException | JCodecException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getFirstFrameBase64FromUrl(String videoUrl) {
        File tempFile = null;
        try {
            // 1. 下载视频到临时文件
            tempFile = File.createTempFile("video_temp_", ".tmp");
            try (InputStream in = new URL(videoUrl).openStream();
                 OutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            // 2. 提取首帧
            String base64 = getFirstFrameBase64(tempFile);
            return base64;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 3. 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
        return null;
    }


    @Override
    public void getVideoById(Long id, HttpServletRequest request, HttpServletResponse response) {
        NoticeAnnouncementImage noticeAnnouncementVideo = noticeAnnouncementImageMapper.selectById(id);
        Path videoPath = Paths.get(noticeAnnouncementVideo.getFileUrl());
        if (!videoPath.toFile().exists()) {
            return;
        }
        try (RandomAccessFile videoFile = new RandomAccessFile(videoPath.toFile(), "r")) {
            long fileLength = videoFile.length();
            String range = request.getHeader("Range");
            // 设置响应头
            response.setContentType("video/mp4");
            response.setHeader("Accept-Ranges", "bytes");
            long start = 0;
            long end = fileLength - 1;
            // 如果客户端请求了 Range
            if (range != null && range.startsWith("bytes=")) {
                String[] ranges = range.substring(6).split("-");
                try {
                    start = Long.parseLong(ranges[0]);
                    if (ranges.length > 1) {
                        end = Long.parseLong(ranges[1]);
                    }
                } catch (NumberFormatException e) {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
            }
            // 校验范围合法性
            if (start > end || end >= fileLength) {
                response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
                return;
            }
            // 设置 Content-Range 响应头
            response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
            response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + fileLength);
            response.setHeader("Content-Length", String.valueOf(end - start + 1));
            // 读取指定范围的数据并写入响应
            byte[] buffer = new byte[8192];
            videoFile.seek(start);
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                long bytesToWrite = end - start + 1;
                while (bytesToWrite > 0) {
                    int bytesRead = videoFile.read(buffer, 0, (int) Math.min(buffer.length, bytesToWrite));
                    if (bytesRead == -1) {
                        break;
                    }
                    outputStream.write(buffer, 0, bytesRead);
                    bytesToWrite -= bytesRead;
                }
                outputStream.flush();
            }
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    private LambdaQueryWrapper<NoticeAnnouncement> buildQueryWrapper(NoticeAnnouncementQueryDto query) {
        LambdaQueryWrapper<NoticeAnnouncement> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtil.notNullNorEmpty(query.getTitle()), NoticeAnnouncement::getTitle, query.getTitle());
        lqw.eq(ObjectUtil.isNotEmpty(query.getType()), NoticeAnnouncement::getType, query.getType());
        return lqw;
    }


    @Override
    public NoticeConfig getNoticeConfig() {
        return noticeConfigMapper.selectOne(null);
    }

    @Override
    public boolean updateNoticeConfig(NoticeConfig noticeConfig) {
        return noticeConfigMapper.updateById(noticeConfig) > 0;
    }
}
