package cn.iocoder.yudao.module.notice.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 班牌通知公告视频对象 notice_announcement_video
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
@NoArgsConstructor
@Accessors(chain = true)
@TableName("notice_announcement_video")
public class NoticeAnnouncementVideo
{
    private static final long serialVersionUID = 1L;

    /** 主键ID，雪花算法生成 */
    @TableId(type= IdType.INPUT)
    private Long id;

    /** 公告ID，逻辑关联announcement表 */
    private Long announcementId;

    /** 视频地址 */
    private String videoUrl;

    /** 视频封面地址 */
    private String coverUrl;

    /** 状态，0启用，1禁用 */
    private String status;

    private Date createTime;

    private Date updateTime;

}
