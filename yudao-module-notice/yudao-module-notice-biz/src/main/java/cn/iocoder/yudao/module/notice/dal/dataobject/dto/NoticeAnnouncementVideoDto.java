package cn.iocoder.yudao.module.notice.dal.dataobject.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 班牌通知公告视频对象 notice_announcement_video
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
public class NoticeAnnouncementVideoDto implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键ID，雪花算法生成 */
    private Long id;

    /** 公告ID，逻辑关联announcement表 */
    private Long announcementId;

    /** 视频地址 */
    private String videoUrl;

    /** 视频封面地址 */
    private String coverUrl;

    private Date createTime;

    private Date updateTime;

}
