package cn.iocoder.yudao.module.notice.dal.dataobject.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 班牌通知公告主对象 notice_announcement
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
@Schema(description = "班牌通知公告 - 文件上传")
public class NoticeAnnouncementFileDto implements Serializable
{
    @Schema(description = "文件对象")
    private MultipartFile file;

    /** 图片地址 */
    private String fileUrl;

    @Schema(description = "文件id")
    private Long id;

    @Schema(description = "上传的文件类型，区分图片还是视频，0图片，1视频")
    private String fileType;

    /** 图片注解，仅上传图片的时候传入 */
    @Schema(description = "图片注解，仅上传图片的时候传入")
    private String caption;


    /** 排序号，越小越靠前，仅上传图片的时候传入 */
    @Schema(description = "排序号，越小越靠前，仅上传图片的时候传入")
    private Long sortOrder;

    /** 状态，0启用，1禁用 */
    @Schema(description = "状态，0启用，1禁用")
    private String status;



}
