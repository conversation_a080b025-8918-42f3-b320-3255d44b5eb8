package cn.iocoder.yudao.module.notice.dal.dataobject.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 班牌通知公告图片轮播对象 notice_announcement_image
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@ToString
public class NoticeAnnouncementImageVo {
    private static final long serialVersionUID = 1L;

    /** 主键ID，雪花算法生成 */

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 公告ID，逻辑关联announcement表 */
    private Long announcementId;

    /** 图片地址 */
    private String imageUrl;

    /** 图片注解 */
    private String caption;

    /** 排序号，越小越靠前 */
    private Long sortOrder;

    private Date createTime;

    private Date updateTime;



    /** 状态，0启用，1禁用 */
    private String status;

    //文件存储路径
    private String fileUrl;

    // 文件类型
    private String fileType;
    // 文件名
    private String fileName;


}
