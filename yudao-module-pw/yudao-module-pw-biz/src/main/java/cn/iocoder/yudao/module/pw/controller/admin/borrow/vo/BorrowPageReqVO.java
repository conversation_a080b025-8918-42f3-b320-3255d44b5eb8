package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 借用表单关联物品新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BorrowPageReqVO extends PageParam {


    @Schema(description = "物品")
    private String goodsName;

    @Schema(description = "期望借用时间")
    private String[] expectedBorrowTime;

    @Schema(description = "期望归还时间")
    private String[] expectedReturnTime;


    @Schema(description = "状态")
    private List<Integer> applyStatus;


}