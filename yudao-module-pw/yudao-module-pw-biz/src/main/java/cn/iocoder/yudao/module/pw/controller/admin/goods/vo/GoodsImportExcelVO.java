package cn.iocoder.yudao.module.pw.controller.admin.goods.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class GoodsImportExcelVO {

    @ExcelProperty("物品名称")
    private String name;

    @ExcelProperty("物品简介")
    private String goodsDesc;

    @ExcelProperty("库存数量")
    private Integer amount;

    @ExcelProperty("物品单位")
    private String unit;

    @ExcelProperty(value = "物品状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

}
