package cn.iocoder.yudao.module.pw.controller.admin.pointpermission;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pw.controller.admin.point.vo.PointRespVO;
import cn.iocoder.yudao.module.pw.controller.admin.pointpermission.vo.*;
import cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo.UserGroupImportExcelVO;
import cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo.UserGroupImportRespVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.point.PointDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointgroup.PointGroupDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointgroup.PointGroupRelDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointpermission.PointPermissionDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.region.RegionDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.user.UserDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.usergroup.UserGroupDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.usergroup.UserGroupRelDO;
import cn.iocoder.yudao.module.pw.service.dept.DeptService;
import cn.iocoder.yudao.module.pw.service.hik.sdk.AcsService;
import cn.iocoder.yudao.module.pw.service.point.PointService;
import cn.iocoder.yudao.module.pw.service.pointgroup.PointGroupService;
import cn.iocoder.yudao.module.pw.service.pointpermission.PointPermissionService;
import cn.iocoder.yudao.module.pw.service.region.RegionService;
import cn.iocoder.yudao.module.pw.service.user.UserService;
import cn.iocoder.yudao.module.pw.service.usergroup.UserGroupService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.enums.common.SexEnum;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Slf4j
@Tag(name = "管理后台 - 设备授权")
@RestController
@RequestMapping("/pw/point-permission")
@Validated
public class PointPermissionController {

    @Resource
    private PointPermissionService pointPermissionService;
    @Resource
    private PointService pointService;
    @Resource
    private RegionService regionService;
    @Resource
    private PointGroupService pointGroupService;
    @Resource
    private UserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private UserGroupService userGroupService;

    @Resource
    private AdminUserApi adminUserApi;

    @PostMapping("/create")
    @Operation(summary = "创建权限")
    public CommonResult<Boolean> create(@Valid @RequestBody PointPermissionReqVO pointPermissionReqVO) {
        try {
            return success(pointPermissionService.create(pointPermissionReqVO));
        } catch (ServiceException e) {
            return CommonResult.error(e.getCode(), e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermission('pw:user-point:query')")
    @GetMapping("/point-user")
    @Operation(summary = "查询设备人员")
    public CommonResult<PageResult<PointConfigRespVO>> getPointConfig(@RequestParam("pointId") Long pointId,
                                                                @RequestParam(value = "keyword", required = false) String keyword,
                                                                @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                @RequestParam(value = "pageSize", required = false) Integer pageSize) throws Exception {
        PointDO point = pointService.getPoint(pointId);
        AcsService acsService = new AcsService();
        acsService.login(point.getDeviceIp(), point.getDevicePort(), point.getDeviceAccount(), point.getDevicePassword());
        JSONObject result = acsService.getPageUser(pageNo, pageSize, keyword);
        acsService.logout();
//        String resultStr="{\n" +
//                "    \"UserInfoSearch\": {\n" +
//                "        \"responseStatusStrg\": \"OK\",\n" +
//                "        \"searchID\": \"91263332c9\",\n" +
//                "        \"UserInfo\": [\n" +
//                "            {\n" +
//                "                \"employeeNo\": \"744d8a13e17b4eb0bde165e47683f56e\",\n" +
//                "                \"name\": \"王森淼\",\n" +
//                "                \"userType\": \"normal\",\n" +
//                "                \"closeDelayEnabled\": false,\n" +
//                "                \"Valid\": {\n" +
//                "                    \"enable\": true,\n" +
//                "                    \"beginTime\": \"2020-09-16T00:00:00\",\n" +
//                "                    \"endTime\": \"2037-12-31T23:59:59\",\n" +
//                "                    \"timeType\": \"local\"\n" +
//                "                },\n" +
//                "                \"belongGroup\": \"\",\n" +
//                "                \"password\": \"\",\n" +
//                "                \"doorRight\": \"1\",\n" +
//                "                \"RightPlan\": [\n" +
//                "                    {\n" +
//                "                        \"doorNo\": 1,\n" +
//                "                        \"planTemplateNo\": \"1\"\n" +
//                "                    }\n" +
//                "                ],\n" +
//                "                \"maxOpenDoorTime\": 0,\n" +
//                "                \"openDoorTime\": 0,\n" +
//                "                \"roomNumber\": 99,\n" +
//                "                \"floorNumber\": 999,\n" +
//                "                \"localUIRight\": false,\n" +
//                "                \"numOfCard\": 1,\n" +
//                "                \"numOfFP\": 0,\n" +
//                "                \"numOfFace\": 1\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"employeeNo\": \"afdsfdsa165e47683f56e\",\n" +
//                "                \"name\": \"王森淼2\",\n" +
//                "                \"userType\": \"normal\",\n" +
//                "                \"closeDelayEnabled\": false,\n" +
//                "                \"Valid\": {\n" +
//                "                    \"enable\": true,\n" +
//                "                    \"beginTime\": \"2020-09-16T00: 00: 00\",\n" +
//                "                    \"endTime\": \"2037-12-31T23:59:59\",\n" +
//                "                    \"timeType\": \"local\"\n" +
//                "                },\n" +
//                "                \"belongGroup\": \"\",\n" +
//                "                \"password\": \"\",\n" +
//                "                \"doorRight\": \"1\",\n" +
//                "                \"RightPlan\": [\n" +
//                "                    {\n" +
//                "                        \"doorNo\": 1,\n" +
//                "                        \"planTemplateNo\": \"1\"\n" +
//                "                    }\n" +
//                "                ],\n" +
//                "                \"maxOpenDoorTime\": 0,\n" +
//                "                \"openDoorTime\": 0,\n" +
//                "                \"roomNumber\": 99,\n" +
//                "                \"floorNumber\": 999,\n" +
//                "                \"localUIRight\": false,\n" +
//                "                \"numOfCard\": 1,\n" +
//                "                \"numOfFP\": 0,\n" +
//                "                \"numOfFace\": 1\n" +
//                "            }\n" +
//                "        ],\n" +
//                "        \"numOfMatches\": 3,\n" +
//                "        \"totalMatches\": 4\n" +
//                "    }\n" +
//                "}";
//        JSONObject result = new JSONObject(resultStr);
//        String o = JSON.toJSONString(result);
        Long totalMatches = result.getJSONObject("UserInfoSearch").getLong("totalMatches");
        if(totalMatches == 0){
            return success(new PageResult(Lists.newArrayList(), totalMatches));
        }
        JSONArray userInfoArray = result.getJSONObject("UserInfoSearch").getJSONArray("UserInfo");

        List<String> usernames = Lists.newArrayList();
        Map<String, JSONObject> deviceUserMap = new HashMap<>();
        List<PointConfigRespVO> resultList = new ArrayList<>();
        for(int i=0; i < userInfoArray.length() ; i++){
            JSONObject jsonObject = userInfoArray.getJSONObject(i);
            String employeeNo = jsonObject.getString("employeeNo");
            String name = jsonObject.getString("name");
            usernames.add(employeeNo);
            deviceUserMap.put(employeeNo, jsonObject);
            PointConfigRespVO pointConfigRespVO = new PointConfigRespVO();
            pointConfigRespVO.setId(point.getId());
                try {
                    pointConfigRespVO.setPassword(jsonObject.getString("password"));
                    JSONObject valid = jsonObject.getJSONObject("Valid");
                    boolean enable = valid.getBoolean("enable");
                    if (enable) {
                        LocalDateTime endTime = LocalDateTime.parse(valid.getString("endTime"));
                        pointConfigRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(endTime));
                    }
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
            pointConfigRespVO.setUsername(employeeNo);
            pointConfigRespVO.setNickname(name);
            resultList.add(pointConfigRespVO);
        }
        return success(new PageResult(resultList,totalMatches));
    }



//    @PreAuthorize("@ss.hasPermission('pw:user-point:export')")
    @PermitAll
    @GetMapping("/point-user-export")
    @ApiAccessLog(operateType = EXPORT)
    @Operation(summary = "导出设备人员")
    public void exportPointUser(@RequestParam("pointId") Long pointId,
                                @RequestParam(value = "keyword", required = false) String keyword,
                                @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                HttpServletResponse response) throws Exception {
        PointDO point = pointService.getPoint(pointId);
        AcsService acsService = new AcsService();
        acsService.login(point.getDeviceIp(), point.getDevicePort(), point.getDeviceAccount(), point.getDevicePassword());
        JSONObject result = acsService.getPageUser(pageNo, pageSize, keyword);
        acsService.logout();
//        String resultStr="{\n" +
//                "    \"UserInfoSearch\": {\n" +
//                "        \"responseStatusStrg\": \"OK\",\n" +
//                "        \"searchID\": \"91263332c9\",\n" +
//                "        \"UserInfo\": [\n" +
//                "            {\n" +
//                "                \"employeeNo\": \"744d8a13e17b4eb0bde165e47683f56e\",\n" +
//                "                \"name\": \"王森淼\",\n" +
//                "                \"userType\": \"normal\",\n" +
//                "                \"closeDelayEnabled\": false,\n" +
//                "                \"Valid\": {\n" +
//                "                    \"enable\": true,\n" +
//                "                    \"beginTime\": \"2020-09-16T00:00:00\",\n" +
//                "                    \"endTime\": \"2037-12-31T23:59:59\",\n" +
//                "                    \"timeType\": \"local\"\n" +
//                "                },\n" +
//                "                \"belongGroup\": \"\",\n" +
//                "                \"password\": \"\",\n" +
//                "                \"doorRight\": \"1\",\n" +
//                "                \"RightPlan\": [\n" +
//                "                    {\n" +
//                "                        \"doorNo\": 1,\n" +
//                "                        \"planTemplateNo\": \"1\"\n" +
//                "                    }\n" +
//                "                ],\n" +
//                "                \"maxOpenDoorTime\": 0,\n" +
//                "                \"openDoorTime\": 0,\n" +
//                "                \"roomNumber\": 99,\n" +
//                "                \"floorNumber\": 999,\n" +
//                "                \"localUIRight\": false,\n" +
//                "                \"numOfCard\": 1,\n" +
//                "                \"numOfFP\": 0,\n" +
//                "                \"numOfFace\": 1\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"employeeNo\": \"afdsfdsa165e47683f56e\",\n" +
//                "                \"name\": \"王森淼2\",\n" +
//                "                \"userType\": \"normal\",\n" +
//                "                \"closeDelayEnabled\": false,\n" +
//                "                \"Valid\": {\n" +
//                "                    \"enable\": true,\n" +
//                "                    \"beginTime\": \"2020-09-16T00: 00: 00\",\n" +
//                "                    \"endTime\": \"2037-12-31T23:59:59\",\n" +
//                "                    \"timeType\": \"local\"\n" +
//                "                },\n" +
//                "                \"belongGroup\": \"\",\n" +
//                "                \"password\": \"\",\n" +
//                "                \"doorRight\": \"1\",\n" +
//                "                \"RightPlan\": [\n" +
//                "                    {\n" +
//                "                        \"doorNo\": 1,\n" +
//                "                        \"planTemplateNo\": \"1\"\n" +
//                "                    }\n" +
//                "                ],\n" +
//                "                \"maxOpenDoorTime\": 0,\n" +
//                "                \"openDoorTime\": 0,\n" +
//                "                \"roomNumber\": 99,\n" +
//                "                \"floorNumber\": 999,\n" +
//                "                \"localUIRight\": false,\n" +
//                "                \"numOfCard\": 1,\n" +
//                "                \"numOfFP\": 0,\n" +
//                "                \"numOfFace\": 1\n" +
//                "            }\n" +
//                "        ],\n" +
//                "        \"numOfMatches\": 3,\n" +
//                "        \"totalMatches\": 4\n" +
//                "    }\n" +
//                "}";
//        JSONObject result = new JSONObject(resultStr);
        String o = JSON.toJSONString(result);
        log.info("point-user :{}", o);
        System.out.println("point-user"+ o );
        Long totalMatches = result.getJSONObject("UserInfoSearch").getLong("totalMatches");
        if(totalMatches == 0){
            throw new ServiceException(-1,"设备人员为空");
        }
        JSONArray userInfoArray = result.getJSONObject("UserInfoSearch").getJSONArray("UserInfo");

        List<String> usernames = Lists.newArrayList();
        Map<String, JSONObject> deviceUserMap = new HashMap<>();
        List<PointConfigRespVO> resultList = new ArrayList<>();
        for(int i=0; i < userInfoArray.length() ; i++){
            JSONObject jsonObject = userInfoArray.getJSONObject(i);
            String employeeNo = jsonObject.getString("employeeNo");
            String name = jsonObject.getString("name");
            usernames.add(employeeNo);
            deviceUserMap.put(employeeNo, jsonObject);
            PointConfigRespVO pointConfigRespVO = new PointConfigRespVO();
            pointConfigRespVO.setId(point.getId());
            try {
                pointConfigRespVO.setPassword(jsonObject.getString("password"));
                JSONObject valid = jsonObject.getJSONObject("Valid");
                boolean enable = valid.getBoolean("enable");
                if (enable) {
                    LocalDateTime endTime = LocalDateTime.parse(valid.getString("endTime"));
                    pointConfigRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(endTime));
                }
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            pointConfigRespVO.setUsername(employeeNo);
            pointConfigRespVO.setNickname(name);
            resultList.add(pointConfigRespVO);
        }
        // 导出 Excel
        ExcelUtils.write(response, point.getDeviceIp()+"设备人员.xls", "设备数据", PointConfigRespVO.class, resultList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入删除设备端用户")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
    })
    @PreAuthorize("@ss.hasPermission('pw:user-group:import')")
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file) throws IOException {
        List<PointConfigRespVO> list = ExcelUtils.read(file, PointConfigRespVO.class);
        if(CollectionUtils.isAnyEmpty(list)){
            throw new ServiceException(-1," 导入excel不能为空");
        }
        Map<Long, List<PointConfigRespVO>> pointIdMap = list.stream().filter(item -> Objects.nonNull(item.getId())).collect(Collectors.groupingBy(PointConfigRespVO::getId));
        for(Long pointId : pointIdMap.keySet()){
            List<PointConfigRespVO> pointConfigRespVOS = pointIdMap.get(pointId);
            Set<String> usernameList = pointConfigRespVOS.stream().map(PointConfigRespVO::getUsername).collect(Collectors.toSet());
            List<List<String>> partition = Lists.partition(Lists.newArrayList(usernameList), 50);
            partition.forEach(item->{
                pointPermissionService.deleteByDeviceUser(pointId, item);
            });

        }
        return success(true);
    }

    @PreAuthorize("@ss.hasPermission('pw:user-point:query')")
    @GetMapping("/point-user-group")
    @Operation(summary = "查询点位组权限")
    public CommonResult<List<PointGroupConfigRespVO>> getPointGroupConfig(@RequestParam("pointId") Long pointId) {
        List<PointPermissionDO> userConfig = pointPermissionService.getPointConfig(pointId).stream().filter(p -> p.getUserType() == 1).toList();
        Set<Long> userGroupIds = userConfig.stream().map(PointPermissionDO::getUserId).collect(Collectors.toSet());
        List<UserGroupDO> userGroupDOS = userGroupService.getUserGroupDOS(userGroupIds);
        Map<Long, UserGroupDO> userGroupDOMap = userGroupDOS.stream().collect(Collectors.toMap(UserGroupDO::getId, g -> g));

        List<PointGroupConfigRespVO> list = userConfig.stream().map(p -> {
            PointGroupConfigRespVO pointGroupConfigRespVO = new PointGroupConfigRespVO();
            pointGroupConfigRespVO.setId(p.getId()).setPointId(p.getPointId()).setUserGroupId(p.getUserId()).setExpired(p.getExpired());
            ;
            if (p.getExpired() == 1) {
                if (p.getExpiredBegin() != null) {
                    pointGroupConfigRespVO.setExpiredBegin(DateUtil.formatLocalDateTime(p.getExpiredBegin()));
                }
                if (p.getExpiredEnd() != null) {
                    pointGroupConfigRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(p.getExpiredEnd()));
                }
            }
            UserGroupDO userGroupDO = userGroupDOMap.get(p.getUserId());
            if (userGroupDO != null) {
                pointGroupConfigRespVO.setUserGroupName(userGroupDO.getName());
            }

            return pointGroupConfigRespVO;
        }).toList();


        return success(list);
    }

    @PreAuthorize("@ss.hasPermission('pw:user-point:create')")
    @GetMapping("/user-point")
    @Operation(summary = "查询用户点位权限")
    public CommonResult<List<UserConfigRespVO>> getUserConfig(@RequestParam("userId") Long userId) {
        List<PointPermissionDO> userConfig = pointPermissionService.getUserConfig(userId).stream().filter(p -> p.getPointType() == 0).toList();

        Set<Long> pointIds = userConfig.stream().map(PointPermissionDO::getPointId).collect(Collectors.toSet());
        List<PointDO> points = pointService.getPoints(pointIds);
        Map<Long, PointDO> pointDOMap = points.stream().collect(Collectors.toMap(PointDO::getId, p -> p));
        Set<Long> regionIds = points.stream().map(PointDO::getRegionId).collect(Collectors.toSet());
        List<RegionDO> regions = regionService.getRegionList(regionIds);
        Map<Long, RegionDO> regionDOMap = regions.stream().collect(Collectors.toMap(RegionDO::getId, p -> p));


        List<UserConfigRespVO> list = userConfig.stream().map(p -> {
            UserConfigRespVO userConfigRespVO = new UserConfigRespVO().setId(p.getId()).setUserId(p.getUserId()).setPointId(p.getPointId()).setExpired(p.getExpired());
            if (p.getExpired() == 1) {
                if (p.getExpiredBegin() != null) {
                    userConfigRespVO.setExpiredBegin(DateUtil.formatLocalDateTime(p.getExpiredBegin()));
                }
                if (p.getExpiredEnd() != null) {
                    userConfigRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(p.getExpiredEnd()));
                }
            }
            PointDO pointDO = pointDOMap.get(p.getPointId());
            if (pointDO != null) {
                userConfigRespVO.setPointName(pointDO.getPointName());
                userConfigRespVO.setRegionId(pointDO.getRegionId());
                RegionDO regionDO = regionDOMap.get(pointDO.getRegionId());
                if (regionDO != null) {
                    userConfigRespVO.setRegionName(regionDO.getName());
                }
            }

            return userConfigRespVO;
        }).toList();


        return success(list);

    }


    @PreAuthorize("@ss.hasPermission('pw:user-point:query')")
    @GetMapping("/user-point-group")
    @Operation(summary = "查询用户点位组权限")
    public CommonResult<List<UserGroupConfigRespVO>> getUserGroupConfig(@RequestParam("userId") Long userId) {
        List<PointPermissionDO> userConfig = pointPermissionService.getUserConfig(userId).stream().filter(p -> p.getPointType() == 1).toList();
        Set<Long> pointGroupIds = userConfig.stream().map(PointPermissionDO::getPointId).collect(Collectors.toSet());
        List<PointGroupDO> pointGroupDOS = pointGroupService.getPointGroupDOS(pointGroupIds);
        Map<Long, PointGroupDO> pointGroupDOMap = pointGroupDOS.stream().collect(Collectors.toMap(PointGroupDO::getId, g -> g));

        List<UserGroupConfigRespVO> list = userConfig.stream().map(p -> {
            UserGroupConfigRespVO userGroupConfigRespVO = new UserGroupConfigRespVO();
            userGroupConfigRespVO.setId(p.getId()).setUserId(p.getUserId()).setPointGroupId(p.getPointId()).setExpired(p.getExpired());
            ;
            if (p.getExpired() == 1) {
                if (p.getExpiredBegin() != null) {
                    userGroupConfigRespVO.setExpiredBegin(DateUtil.formatLocalDateTime(p.getExpiredBegin()));
                }
                if (p.getExpiredEnd() != null) {
                    userGroupConfigRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(p.getExpiredEnd()));
                }
            }
            PointGroupDO pointGroupDO = pointGroupDOMap.get(p.getPointId());
            if (pointGroupDO != null) {
                userGroupConfigRespVO.setPointGroupName(pointGroupDO.getName());
            }

            return userGroupConfigRespVO;
        }).toList();


        return success(list);
    }

    @PreAuthorize("@ss.hasPermission('pw:user-point:delete')")
    @DeleteMapping("/delete")
    @Operation(summary = "删除")
    @Parameter(name = "id", required = true)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        pointPermissionService.delete(id);
        return success(true);
    }


    @PreAuthorize("@ss.hasPermission('pw:user-point:delete')")
    @DeleteMapping("/deleteByDeviceUser")
    @Operation(summary = "删除设备人员")
    @Parameter(name = "username", required = true)
    public CommonResult<Boolean> deleteByDeviceUser(@RequestParam("pointId") Long pointId,
                                                    @RequestParam("usernameList") List<String> usernameList) throws UnknownHostException, JSONException, InterruptedException{
        return success(pointPermissionService.deleteByDeviceUser(pointId, usernameList));
    }

    @PreAuthorize("@ss.hasPermission('pw:user-point:query')")
    @GetMapping("/point-permissions")
    @Operation(summary = "查询点位权限明细")
    public CommonResult<List<PointPermissionRespVO>> getPointPermissions(@RequestParam("pointId") Long pointId) {
        List<PointPermissionDO> userConfig = pointPermissionService.getPointConfig(pointId);
        List<PointPermissionRespVO> pointPermissionRespVOS = new ArrayList<>();
        List<PointPermissionDO> pointConfig4user = userConfig.stream().filter(p -> p.getUserType() == 0).toList();
        for (PointPermissionDO p : pointConfig4user) {
            PointPermissionRespVO pointPermissionRespVO = new PointPermissionRespVO();
            pointPermissionRespVO.setUserId(p.getUserId());
            pointPermissionRespVO.setPointId(p.getPointId());
            pointPermissionRespVO.setExpired(p.getExpired());
            pointPermissionRespVO.setExpiredBegin(DateUtil.formatLocalDateTime(p.getExpiredBegin()));
            pointPermissionRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(p.getExpiredEnd()));
            pointPermissionRespVO.setUpdateTime(p.getUpdateTime());
            pointPermissionRespVOS.add(pointPermissionRespVO);
        }

        List<PointPermissionDO> pointConfig4group = userConfig.stream().filter(p -> p.getUserType() == 1).toList();
        List<Long> groupIds = pointConfig4group.stream().map(PointPermissionDO::getUserId).toList();
        List<UserGroupRelDO> userGroupRelDOS = userGroupService.getUserGroupRelDOS(groupIds);

        for (PointPermissionDO p : pointConfig4group) {
            Long groupId = p.getUserId();
            List<Long> userIds = userGroupRelDOS.stream().filter(r -> r.getGroupId().equals(groupId)).map(UserGroupRelDO::getUserId).toList();
            for (Long userId : userIds) {
                PointPermissionRespVO pointPermissionRespVO = new PointPermissionRespVO();
                pointPermissionRespVO.setUserId(userId);
                pointPermissionRespVO.setPointId(p.getPointId());
                pointPermissionRespVO.setExpired(p.getExpired());
                pointPermissionRespVO.setExpiredBegin(DateUtil.formatLocalDateTime(p.getExpiredBegin()));
                pointPermissionRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(p.getExpiredEnd()));
                pointPermissionRespVO.setUpdateTime(p.getUpdateTime());
                pointPermissionRespVOS.add(pointPermissionRespVO);
            }
        }

        //求最新的点位
        List<PointPermissionRespVO> result = filterMaxTimeItems2(pointPermissionRespVOS);
        //封装数据
        Set<Long> userIds = result.stream().map(PointPermissionRespVO::getUserId).collect(Collectors.toSet());
        List<UserDO> users = userService.getUsers(userIds);
        Map<Long, UserDO> userDOMap = users.stream().collect(Collectors.toMap(UserDO::getId,    // 键：User::getId
                p -> p    // 值：User 对象本身
        ));
        Set<Long> deptIds = users.stream().map(UserDO::getDeptId).collect(Collectors.toSet());
        List<DeptDO> deptList = deptService.getDeptList(deptIds);
        Map<Long, DeptDO> deptDOMap = deptList.stream().collect(Collectors.toMap(DeptDO::getId, p -> p));
        for (PointPermissionRespVO p : result) {
            UserDO userDO = userDOMap.get(p.getUserId());
            if (userDO != null) {
                p.setUsername(userDO.getUsername());
                p.setNickname(userDO.getNickname());
                p.setDeptId(userDO.getDeptId());
                DeptDO deptDO = deptDOMap.get(userDO.getDeptId());
                if (deptDO != null) {
                    p.setDeptName(deptDO.getName());
                }
            }
        }

        return success(result);
    }

    public static List<PointPermissionRespVO> filterMaxTimeItems2(List<PointPermissionRespVO> items) {
        // 使用Stream API进行分组和筛选
        Map<Long, PointPermissionRespVO> maxTimeItems = items.stream().collect(Collectors.toMap(PointPermissionRespVO::getUserId, item -> item, (existing, replacement) -> existing.getUpdateTime().isAfter(replacement.getUpdateTime()) ? existing : replacement));

        // 将Map中的值转换为List
        return new ArrayList<>(maxTimeItems.values());
    }

    /**
     * 用户的点位, 点位组求并集,如果同时存在点位和点位组的话,取最新时间的点位
     *
     * @param userId 人员id
     * @return 点位权限明细
     */
    @PreAuthorize("@ss.hasPermission('pw:user-point:query')")
    @GetMapping("/user-permissions")
    @Operation(summary = "查询用户点位权限明细")
    public CommonResult<List<UserPermissionRespVO>> getUserPermissions(@RequestParam("userId") Long userId) {
        List<PointPermissionDO> userConfig = pointPermissionService.getUserConfig(userId);

        List<UserPermissionRespVO> userPermissionRespVOS = new ArrayList<>();

        List<PointPermissionDO> userConfig4point = userConfig.stream().filter(p -> p.getPointType() == 0).toList();

        for (PointPermissionDO p : userConfig4point) {
            UserPermissionRespVO userPermissionRespVO = new UserPermissionRespVO();
            userPermissionRespVO.setUserId(p.getUserId());
            userPermissionRespVO.setPointId(p.getPointId());
            userPermissionRespVO.setExpired(p.getExpired());
            userPermissionRespVO.setExpiredBegin(DateUtil.formatLocalDateTime(p.getExpiredBegin()));
            userPermissionRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(p.getExpiredEnd()));
            userPermissionRespVO.setUpdateTime(p.getUpdateTime());
            userPermissionRespVOS.add(userPermissionRespVO);
        }

        List<PointPermissionDO> userConfig4group = userConfig.stream().filter(p -> p.getPointType() == 1).toList();

        List<Long> groupIds = userConfig4group.stream().map(PointPermissionDO::getPointId).toList();
        List<PointGroupRelDO> pointGroupRelDOS = pointGroupService.getPointGroupRelDOS(groupIds);

        for (PointPermissionDO p : userConfig4group) {
            Long groupId = p.getPointId();
            List<Long> pointIds = pointGroupRelDOS.stream().filter(r -> r.getGroupId().equals(groupId)).map(PointGroupRelDO::getPointId).toList();
            for (Long pointId : pointIds) {
                UserPermissionRespVO userPermissionRespVO = new UserPermissionRespVO();
                userPermissionRespVO.setUserId(p.getUserId());
                userPermissionRespVO.setPointId(pointId);
                userPermissionRespVO.setExpired(p.getExpired());
                userPermissionRespVO.setExpiredBegin(DateUtil.formatLocalDateTime(p.getExpiredBegin()));
                userPermissionRespVO.setExpiredEnd(DateUtil.formatLocalDateTime(p.getExpiredEnd()));
                userPermissionRespVO.setUpdateTime(p.getUpdateTime());
                userPermissionRespVOS.add(userPermissionRespVO);
            }
        }

        //求最新的点位
        List<UserPermissionRespVO> result = filterMaxTimeItems(userPermissionRespVOS);
        //封装数据
        Set<Long> pointIds = result.stream().map(UserPermissionRespVO::getPointId).collect(Collectors.toSet());
        List<PointDO> points = pointService.getPoints(pointIds);
        Map<Long, PointDO> pointDOMap = points.stream().collect(Collectors.toMap(PointDO::getId,    // 键：User::getId
                p -> p    // 值：User 对象本身
        ));
        Set<Long> regionIds = points.stream().map(PointDO::getRegionId).collect(Collectors.toSet());
        List<RegionDO> regions = regionService.getRegionList(regionIds);
        Map<Long, RegionDO> regionDOMap = regions.stream().collect(Collectors.toMap(RegionDO::getId, p -> p));
        for (UserPermissionRespVO p : result) {
            PointDO pointDO = pointDOMap.get(p.getPointId());
            if (pointDO != null) {
                p.setPointName(pointDO.getPointName());
                p.setRegionId(pointDO.getRegionId());
                RegionDO regionDO = regionDOMap.get(pointDO.getRegionId());
                if (regionDO != null) {
                    p.setRegionName(regionDO.getName());
                }
            }
        }

        return success(result);

    }

    /**
     * 过滤出每个id对应的time最大的Item
     *
     * @param items 原始集合
     * @return 过滤后的集合
     */
    public static List<UserPermissionRespVO> filterMaxTimeItems(List<UserPermissionRespVO> items) {
        // 使用Stream API进行分组和筛选
        Map<Long, UserPermissionRespVO> maxTimeItems = items.stream().collect(Collectors.toMap(UserPermissionRespVO::getPointId, item -> item, (existing, replacement) -> existing.getUpdateTime().isAfter(replacement.getUpdateTime()) ? existing : replacement));

        // 将Map中的值转换为List
        return new ArrayList<>(maxTimeItems.values());
    }
}
