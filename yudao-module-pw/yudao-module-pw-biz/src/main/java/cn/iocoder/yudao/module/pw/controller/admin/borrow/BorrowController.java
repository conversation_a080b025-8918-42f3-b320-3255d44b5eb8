package cn.iocoder.yudao.module.pw.controller.admin.borrow;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pw.controller.admin.borrow.vo.*;
import cn.iocoder.yudao.module.pw.dal.dataobject.borrow.BorrowDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.borrow.BorrowGoodsDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.borrow.BorrowImageDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.borrow.BorrowProcessDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.goods.GoodsDO;
import cn.iocoder.yudao.module.pw.service.borrow.BorrowGoodsService;
import cn.iocoder.yudao.module.pw.service.borrow.BorrowImageService;
import cn.iocoder.yudao.module.pw.service.borrow.BorrowProcessService;
import cn.iocoder.yudao.module.pw.service.borrow.BorrowService;
import cn.iocoder.yudao.module.pw.service.goods.GoodsService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 物资借用")
@RestController
@RequestMapping("/pw/borrow")
@Validated
public class BorrowController {
    @Resource
    private BorrowService borrowService;
    @Resource
    private BorrowImageService borrowImageService;
    @Resource
    private BorrowGoodsService borrowGoodsService;
    @Resource
    private BorrowProcessService borrowProcessService;
    @Resource
    private GoodsService goodsService;
    @Resource
    private AdminUserApi userApi;

    @PostMapping("/create")
    @Operation(summary = "创建物资申请表单")
    @PreAuthorize("@ss.hasPermission('pw:borrow:create')")
    public CommonResult<Long> createForm(@Valid @RequestBody BorrowSaveReqVO borrowSaveReqVO) {
        return success(borrowService.createForm(borrowSaveReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物资申请表单")
    @PreAuthorize("@ss.hasPermission('pw:borrow:update')")
    public CommonResult<Boolean> updateForm(@Valid @RequestBody BorrowSaveReqVO borrowSaveReqVO) {
        borrowService.updateForm(borrowSaveReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物资申请表单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pw:borrow:delete')")
    public CommonResult<Boolean> deleteForm(@RequestParam("id") Long id) {
        borrowService.deleteForm(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物资申请表单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pw:borrow:query')")
    public CommonResult<BorrowRespVO> get(@RequestParam("id") Long id) {
        BorrowDO borrowDO = borrowService.get(id);
        //组装数据
        BorrowRespVO borrowRespVO = convertBorrowRespVO(borrowDO);
        AdminUserRespDTO userRespDTO = userApi.getUser(borrowDO.getApplyId()).getData();
        if (userRespDTO != null) {
            borrowRespVO.setApplyName(userRespDTO.getNickname());
        }

        List<BorrowGoodsDO> borrowGoodsDO = borrowGoodsService.getBorrowGoodsDO(borrowDO.getId());
        Set<Long> goodIds = borrowGoodsDO.stream().map(BorrowGoodsDO::getGoodsId).collect(Collectors.toSet());
        Map<Long, GoodsDO> goodsDOMap = goodsService.getGoodsList(goodIds).stream()
                .collect(Collectors.toMap(GoodsDO::getId, p -> p));
        List<BorrowGoodsRespVO> borrowGoodsRespVOS = convertBorrowGoodsRespVO(borrowGoodsDO, goodsDOMap);
        borrowRespVO.setBorrowGoodsRespVOS(borrowGoodsRespVOS);

        List<BorrowImageDO> borrowImageDO = borrowImageService.getBorrowImageDO(borrowDO.getId());
        List<BorrowImageRespVO> borrowImageRespVOS = convertBorrowImageRespVO(borrowImageDO);
        borrowRespVO.setBorrowImageRespVOS(borrowImageRespVOS);

        List<BorrowProcessDO> borrowProcessDO = borrowProcessService.getBorrowProcessDO(borrowDO.getId());
        Set<Long> processIds = borrowProcessDO.stream().map(BorrowProcessDO::getProcessId).collect(Collectors.toSet());
        Map<Long, AdminUserRespDTO> userRespDTOMap = userApi.getUserList(processIds).getData().stream()
                .collect(Collectors.toMap(AdminUserRespDTO::getId, p -> p));
        List<BorrowProcessRespVO> borrowProcessRespVOS = convertBorrowProcessRespVO(borrowProcessDO, userRespDTOMap);
        borrowRespVO.setBorrowProcessRespVOS(borrowProcessRespVOS);


        return success(borrowRespVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得物资申请表单分页")
    @PreAuthorize("@ss.hasPermission('pw:borrow:query')")
    public CommonResult<PageResult<BorrowRespVO>> getPage(@Valid BorrowPageReqVO borrowPageReqVO) {
        PageResult<BorrowDO> pageResult = borrowService.getPage(borrowPageReqVO,null);
        List<BorrowDO> borrowDOS = pageResult.getList();
        if (CollectionUtils.isAnyEmpty(borrowDOS)) {
            return success(new PageResult<BorrowRespVO>(Collections.emptyList(), pageResult.getTotal()));
        }
        Set<Long> applyIds = borrowDOS.stream().map(BorrowDO::getApplyId).collect(Collectors.toSet());

        Set<Long> borrowIds = borrowDOS.stream().map(BorrowDO::getId).collect(Collectors.toSet());
        List<BorrowGoodsDO> borrowGoodsDO = borrowGoodsService.getBorrowGoodsDO(borrowIds);
        Set<Long> goodIds = borrowGoodsDO.stream().map(BorrowGoodsDO::getGoodsId).collect(Collectors.toSet());
        Map<Long, GoodsDO> goodsDOMap = goodsService.getGoodsList(goodIds).stream()
                .collect(Collectors.toMap(GoodsDO::getId, p -> p));
        Map<Long, List<BorrowGoodsDO>> borrowGoodsDOMap = borrowGoodsDO.stream()
                .collect(Collectors.groupingBy(BorrowGoodsDO::getBorrowId, Collectors.toList()));

        List<BorrowImageDO> borrowImageDO = borrowImageService.getBorrowImageDO(borrowIds);
        Map<Long, List<BorrowImageDO>> borrowImageDOMap = borrowImageDO.stream()
                .collect(Collectors.groupingBy(BorrowImageDO::getBorrowId, Collectors.toList()));

        List<BorrowProcessDO> borrowProcessDO = borrowProcessService.getBorrowProcessDO(borrowIds);
        Set<Long> processIds = borrowProcessDO.stream().map(BorrowProcessDO::getProcessId).collect(Collectors.toSet());
        Map<Long, List<BorrowProcessDO>> borrowProcessDOMap = borrowProcessDO.stream()
                .collect(Collectors.groupingBy(BorrowProcessDO::getBorrowId, Collectors.toList()));

        applyIds.addAll(processIds);
        Map<Long, AdminUserRespDTO> userRespDTOMap = userApi.getUserList(applyIds).getData().stream()
                .collect(Collectors.toMap(AdminUserRespDTO::getId, p -> p));

        List<BorrowRespVO> borrowRespVOS = new ArrayList<>();
        for (BorrowDO borrowDO : borrowDOS) {
            BorrowRespVO borrowRespVO = convertBorrowRespVO(borrowDO);
            AdminUserRespDTO applyUser = userRespDTOMap.get(borrowRespVO.getApplyId());
            if (applyUser != null) {
                borrowRespVO.setApplyName(applyUser.getNickname());
            }

            List<BorrowGoodsDO> borrowGoodsDOS = borrowGoodsDOMap.get(borrowDO.getId());
            if (!CollectionUtils.isAnyEmpty(borrowGoodsDOS)) {
                borrowRespVO.setBorrowGoodsRespVOS(convertBorrowGoodsRespVO(borrowGoodsDOS, goodsDOMap));
            }

            List<BorrowImageDO> borrowImageDOS = borrowImageDOMap.get(borrowDO.getId());
            if (!CollectionUtils.isAnyEmpty(borrowImageDOS)) {
                borrowRespVO.setBorrowImageRespVOS(convertBorrowImageRespVO(borrowImageDOS));
            }

            List<BorrowProcessDO> borrowProcessDOS = borrowProcessDOMap.get(borrowDO.getId());
            if (!CollectionUtils.isAnyEmpty(borrowProcessDOS)) {
                borrowRespVO.setBorrowProcessRespVOS(convertBorrowProcessRespVO(borrowProcessDOS, userRespDTOMap));
            }

            borrowRespVOS.add(borrowRespVO);
        }

        return success(new PageResult<BorrowRespVO>(borrowRespVOS, pageResult.getTotal()));
    }

    @GetMapping("/export")
    @Operation(summary = "导出物品借用数据")
    @PreAuthorize("@ss.hasPermission('pw:borrow:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void export(@Validated BorrowPageReqVO borrowPageReqVO,
                       HttpServletResponse response) throws IOException {
        borrowPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BorrowDO> borrowDOS = borrowService.getPage(borrowPageReqVO,null).getList();
        if (CollectionUtils.isAnyEmpty(borrowDOS)) {
            ExcelUtils.write(response, "物品借用数据.xls", "物品借用数据", BorrowExcelRespVO.class,
                    Collections.emptyList());
            return;
        }
        Set<Long> applyIds = borrowDOS.stream().map(BorrowDO::getApplyId).collect(Collectors.toSet());
        Map<Long, AdminUserRespDTO> userRespDTOMap = userApi.getUserList(applyIds).getData().stream()
                .collect(Collectors.toMap(AdminUserRespDTO::getId, p -> p));


        Map<Long, BorrowDO> borrowDOMap = borrowDOS.stream().collect(Collectors.toMap(BorrowDO::getId, b -> b));

        Set<Long> borrowIds = borrowDOS.stream().map(BorrowDO::getId).collect(Collectors.toSet());
        List<BorrowGoodsDO> borrowGoodsDO = borrowGoodsService.getBorrowGoodsDO(borrowIds);
        Set<Long> goodIds = borrowGoodsDO.stream().map(BorrowGoodsDO::getGoodsId).collect(Collectors.toSet());
        Map<Long, GoodsDO> goodsDOMap = goodsService.getGoodsList(goodIds).stream()
                .collect(Collectors.toMap(GoodsDO::getId, p -> p));
        List<BorrowExcelRespVO> borrowExcelRespVOS = borrowGoodsDO.stream().map(g -> {
            BorrowExcelRespVO borrowExcelRespVO = new BorrowExcelRespVO();
            borrowExcelRespVO.setBorrowNum(g.getBorrowNum());
            borrowExcelRespVO.setGoodsRemark(g.getRemark());
            GoodsDO goodsDO = goodsDOMap.get(g.getGoodsId());
            if (goodsDO != null) {
                borrowExcelRespVO.setGoodsName(goodsDO.getName());
            }
            BorrowDO borrowDO = borrowDOMap.get(g.getBorrowId());
            if (borrowDO != null) {
                borrowExcelRespVO.setId(borrowDO.getId());
                AdminUserRespDTO applyUser = userRespDTOMap.get(borrowDO.getApplyId());
                borrowExcelRespVO.setApplyName(applyUser != null ? applyUser.getNickname() : "");
                borrowExcelRespVO.setTel(borrowDO.getTel());
                borrowExcelRespVO.setExpectedBorrowTime(DateUtil.formatLocalDateTime(borrowDO.getExpectedBorrowTime()));
                borrowExcelRespVO.setExpectedReturnTime(DateUtil.formatLocalDateTime(borrowDO.getExpectedReturnTime()));
                borrowExcelRespVO.setActualBorrowTime(DateUtil.formatLocalDateTime(borrowDO.getActualBorrowTime()));
                borrowExcelRespVO.setActualReturnTime(DateUtil.formatLocalDateTime(borrowDO.getActualReturnTime()));
                borrowExcelRespVO.setApplyTime(DateUtil.formatLocalDateTime(borrowDO.getApplyTime()));
                borrowExcelRespVO.setApplyStatus(borrowDO.getApplyStatus());
            }
            return borrowExcelRespVO;
        }).toList();

        // 输出 Excel
        ExcelUtils.write(response, "物品借用数据.xls", "物品借用数据", BorrowExcelRespVO.class, borrowExcelRespVOS
        );
    }

    @PostMapping("/create/process")
    @Operation(summary = "创建审批表单")
    @PreAuthorize("@ss.hasPermission('pw:borrow:process')")
    public CommonResult<Long> createProcessForm(@Valid @RequestBody BorrowProcessSaveReqVO borrowProcessSaveReqVO) {
        return success(borrowService.createProcessForm(borrowProcessSaveReqVO));
    }

    @PostMapping("/update/process")
    @Operation(summary = "更新审批表单")
    @PreAuthorize("@ss.hasPermission('pw:borrow:process')")
    public CommonResult<Boolean> updateProcessForm(@Valid @RequestBody BorrowProcessSaveReqVO borrowProcessSaveReqVO) {
        borrowService.updateProcessForm(borrowProcessSaveReqVO);
        return success(true);
    }

    private BorrowRespVO convertBorrowRespVO(BorrowDO borrowDO) {
        BorrowRespVO borrowRespVO = new BorrowRespVO();
        borrowRespVO.setId(borrowDO.getId());
        borrowRespVO.setRemark(borrowDO.getRemark());
        borrowRespVO.setApplyId(borrowDO.getApplyId());
        borrowRespVO.setTel(borrowDO.getTel());
        borrowRespVO.setApplyStatus(borrowDO.getApplyStatus());
        borrowRespVO.setExpectedBorrowTime(DateUtil.formatLocalDateTime(borrowDO.getExpectedBorrowTime()));
        borrowRespVO.setExpectedReturnTime(DateUtil.formatLocalDateTime(borrowDO.getExpectedReturnTime()));
        borrowRespVO.setActualBorrowTime(DateUtil.formatLocalDateTime(borrowDO.getActualBorrowTime()));
        borrowRespVO.setActualReturnTime(DateUtil.formatLocalDateTime(borrowDO.getActualReturnTime()));
        borrowRespVO.setApplyTime(DateUtil.formatLocalDateTime(borrowDO.getApplyTime()));

        return borrowRespVO;

    }

    private List<BorrowGoodsRespVO> convertBorrowGoodsRespVO(List<BorrowGoodsDO> borrowGoodsDO, Map<Long, GoodsDO> goodsDOMap) {
        return borrowGoodsDO.stream().map(g -> {
            BorrowGoodsRespVO borrowGoodsRespVO = BeanUtils.toBean(g, BorrowGoodsRespVO.class);
            GoodsDO goodsDO = goodsDOMap.get(g.getGoodsId());
            if (goodsDO != null) {
                borrowGoodsRespVO.setGoodsName(goodsDO.getName());
                borrowGoodsRespVO.setCover(goodsDO.getCover());
                borrowGoodsRespVO.setAmount(goodsDO.getAmount());
                borrowGoodsRespVO.setMount4borrow(goodsDO.getMount4borrow());
            }
            return borrowGoodsRespVO;
        }).toList();
    }

    private List<BorrowImageRespVO> convertBorrowImageRespVO(List<BorrowImageDO> borrowImageDO) {
        return borrowImageDO.stream()
                .map(i -> BeanUtils.toBean(i, BorrowImageRespVO.class)).toList();
    }

    private List<BorrowProcessRespVO> convertBorrowProcessRespVO(List<BorrowProcessDO> borrowProcessDO, Map<Long, AdminUserRespDTO> userRespDTOMap) {
        return borrowProcessDO.stream().map(p -> {
            BorrowProcessRespVO borrowProcessRespVO = BeanUtils.toBean(p, BorrowProcessRespVO.class);
            borrowProcessRespVO.setProcessTime(DateUtil.formatLocalDateTime(p.getProcessTime()));
            AdminUserRespDTO adminUserRespDTO = userRespDTOMap.get(p.getProcessId());
            if (adminUserRespDTO != null) {
                borrowProcessRespVO.setProcessName(adminUserRespDTO.getNickname());
            }

            return borrowProcessRespVO;
        }).toList();
    }
}
