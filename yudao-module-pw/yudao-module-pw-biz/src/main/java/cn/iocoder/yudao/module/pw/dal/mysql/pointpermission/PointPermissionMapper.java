package cn.iocoder.yudao.module.pw.dal.mysql.pointpermission;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointpermission.PointPermissionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;

@Mapper
public interface PointPermissionMapper extends BaseMapperX<PointPermissionDO> {
    default List<PointPermissionDO> selectByUserIds(List<Long> userIds) {
        if (CollectionUtils.isAnyEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapperX<PointPermissionDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(PointPermissionDO::getUserId, userIds);
        return selectList(queryWrapper);
    }

    default List<PointPermissionDO> selectUserConfig(Long userId){
        LambdaQueryWrapperX<PointPermissionDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(PointPermissionDO::getUserId, userId);
        queryWrapper.eq(PointPermissionDO::getUserType, 0);
        return selectList(queryWrapper);
    }

    default List<PointPermissionDO> selectPointConfig(Long pointId){
        LambdaQueryWrapperX<PointPermissionDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(PointPermissionDO::getPointId, pointId);
        queryWrapper.eq(PointPermissionDO::getPointType, 0);
        return selectList(queryWrapper);
    }
}
