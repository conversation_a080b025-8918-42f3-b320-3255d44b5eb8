package cn.iocoder.yudao.module.pw.dal.mysql.borrow;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.dal.dataobject.borrow.BorrowGoodsDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Mapper
public interface BorrowGoodsMapper extends BaseMapperX<BorrowGoodsDO> {
    default List<BorrowGoodsDO> getByGoodsIds(Collection<Long> goodsIds) {
        if (CollectionUtils.isAnyEmpty(goodsIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapperX<BorrowGoodsDO> wrapperX = new LambdaQueryWrapperX<BorrowGoodsDO>()
                .in(BorrowGoodsDO::getGoodsId, goodsIds);
        return selectList(wrapperX);
    }
}
