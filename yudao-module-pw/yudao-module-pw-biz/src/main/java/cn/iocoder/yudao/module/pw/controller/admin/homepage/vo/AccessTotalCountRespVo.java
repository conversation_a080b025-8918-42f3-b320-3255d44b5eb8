package cn.iocoder.yudao.module.pw.controller.admin.homepage.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/24 11:55
 * @Version 1.0
 */
@Schema(description = "管理后台 - 未归人数的集合")
@Data
public class AccessTotalCountRespVo {


    @Schema(description = "三日未归人数")
    private Integer threeDayOutCount;


    @Schema(description = "七日未归人数")
    private Integer sevenDayOutCount;


    @Schema(description = "总人数")
    private Integer totalCount;


    @Schema(description = "当日未归人数")
    private Integer toDayOutCount;

    @Schema(description = "学院总人数")
    private Integer studentCount;
}
