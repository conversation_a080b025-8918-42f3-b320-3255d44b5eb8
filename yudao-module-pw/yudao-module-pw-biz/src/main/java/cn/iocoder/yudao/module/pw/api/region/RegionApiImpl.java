package cn.iocoder.yudao.module.pw.api.region;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pw.api.region.dto.RegionRespDTO;
import cn.iocoder.yudao.module.pw.controller.admin.region.vo.RegionListReqVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.region.RegionDO;
import cn.iocoder.yudao.module.pw.service.region.RegionService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class RegionApiImpl implements RegionApi {
    @Resource
    private RegionService regionService;

    @Override
    public CommonResult<List<RegionRespDTO>> getRegionList(Collection<Long> ids) {
        List<RegionDO> regionDOList = regionService.getRegionList(ids);
        return success(BeanUtils.toBean(regionDOList, RegionRespDTO.class));
    }

    @Override
    public CommonResult<List<RegionRespDTO>> getRegionListByName(String name) {
            RegionListReqVO query = new RegionListReqVO();
            query.setName(name);
            List<RegionDO> regionList = regionService.getRegionList(query);
            return CommonResult.success(BeanUtils.toBean(regionList, RegionRespDTO.class));
    }
}
