package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 借用表单关联物品新增/修改 Request VO")
@Data
public class BorrowSaveReqVO {

    @Schema(description = "借用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    private Long id;

    @Schema(description = "联系方式")
    private String tel;

    @Schema(description = "期望借用时间")
    private String expectedBorrowTime;

    @Schema(description = "期望归还时间")
    private String expectedReturnTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "借用表单图片列表")
    private List<BorrowImageSaveReqVO> borrowImageSaveReqVOS;
    @Schema(description = "借用物品列表")
    private List<BorrowGoodsSaveReqVO> borrowGoodsSaveReqVOS;


}