package cn.iocoder.yudao.module.pw.controller.admin.pointpermission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 人员点位权限 Resp VO")
@Data
@ToString(callSuper = true)
public class UserConfigRespVO {
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "点位ID")
    private Long pointId;

    @Schema(description = "点位ID")
    private String pointName;

    @Schema(description = "点位所在区域ID")
    private Long regionId;

    @Schema(description = "点位所在区域名称")
    private String regionName;

    @Schema(description = "1：长期有效；2：自定义有效期")
    private Integer expired;

    @Schema(description = "权限开始时间")
    private String expiredBegin;

    @Schema(description = "权限结束时间")
    private String expiredEnd;
}
