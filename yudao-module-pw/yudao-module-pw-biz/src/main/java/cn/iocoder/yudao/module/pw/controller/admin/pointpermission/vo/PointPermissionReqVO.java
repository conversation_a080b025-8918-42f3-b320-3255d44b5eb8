package cn.iocoder.yudao.module.pw.controller.admin.pointpermission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 人员点位权限 Request VO")
@Data
@ToString(callSuper = true)
public class PointPermissionReqVO {
    @Schema(description = "用户ID")
    private List<Long> userIds;

    @Schema(description = "用户组ID")
    private List<Long> userGroupIds;

    @Schema(description = "点位ID")
    private List<Long> pointIds;

    @Schema(description = "点位组ID")
    private List<Long> pointGroupIds;

    @Schema(description = "1：长期有效；2：自定义有效期")
    private Integer expired;

    @Schema(description = "权限开始时间")
    private String expiredBegin;

    @Schema(description = "权限结束时间")
    private String expiredEnd;
}
