package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 借用申请表单物资 Response VO")
@Data
public class BorrowGoodsRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    private Long id;

    @Schema(description = "借用申请表单ID")
    private Long borrowId;

    @Schema(description = "物资ID")
    private Long goodsId;

    @Schema(description = "物品封面")
    private String cover;

    @Schema(description = "物资名称")
    private String goodsName;

    @Schema(description = "库存数量")
    private Integer amount;

    @Schema(description = "已借出数量")
    private Integer mount4borrow;

    @Schema(description = "借用数量")
    private Integer borrowNum;

    @Schema(description = "借用物资备注")
    private String remark;

}