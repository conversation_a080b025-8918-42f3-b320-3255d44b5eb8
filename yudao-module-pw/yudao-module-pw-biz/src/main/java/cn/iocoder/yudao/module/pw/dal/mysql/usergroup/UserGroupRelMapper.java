package cn.iocoder.yudao.module.pw.dal.mysql.usergroup;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.dal.dataobject.usergroup.UserGroupRelDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 权限分组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserGroupRelMapper extends BaseMapperX<UserGroupRelDO> {
    default List<UserGroupRelDO> selectListByGroupId(Long groupId) {
        return selectList(UserGroupRelDO::getGroupId, groupId);
    }

    default List<UserGroupRelDO> selectListByGroupIds(Collection<Long> groupIds) {
        return selectList(UserGroupRelDO::getGroupId, groupIds);
    }

    default void deleteByGroupIdAndUserIds(Long groupId, Collection<Long> userIds) {
        delete(new LambdaQueryWrapperX<UserGroupRelDO>()
                .eq(UserGroupRelDO::getGroupId, groupId)
                .in(UserGroupRelDO::getUserId, userIds));
    }

    default void deleteByGroupId(Long groupId) {
        delete(Wrappers.lambdaUpdate(UserGroupRelDO.class).eq(UserGroupRelDO::getGroupId, groupId));
    }


    default List<Map<String, Object>> selectUserCount(Collection<Long> groupIds) {
        QueryWrapper<UserGroupRelDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.groupBy("group_id")
                .select("group_id AS groupId, COUNT(id) AS count");
        queryWrapper.lambda().in(UserGroupRelDO::getGroupId,groupIds);
        return selectMaps(queryWrapper);
    }
}