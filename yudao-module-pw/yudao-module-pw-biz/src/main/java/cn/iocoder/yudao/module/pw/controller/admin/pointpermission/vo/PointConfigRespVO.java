package cn.iocoder.yudao.module.pw.controller.admin.pointpermission.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 人员点位权限 Resp VO")
@Data
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
@ExcelIgnoreUnannotated
public class PointConfigRespVO {
    @ExcelProperty("设备id")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "工号")
    @ExcelProperty("工号")
    private String username;

    @Schema(description = "姓名")
    @ExcelProperty("姓名")
    private String nickname;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "点位ID")
    private Long pointId;

    @Schema(description = "1：长期有效；2：自定义有效期")
    private Integer expired;

    @Schema(description = "权限开始时间")
    private String expiredBegin;

    @Schema(description = "权限结束时间")
    @ExcelProperty("权限结束时间")
    private String expiredEnd;

    private String password;
}
