package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 借用表单关联物品新增/修改 Request VO")
@Data
public class BorrowExcelRespVO {

    @Schema(description = "物品借用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    @ExcelProperty("物品借用ID")
    private Long id;

    @Schema(description = "借用人")
    @ExcelProperty("借用人")
    private String applyName;

    @Schema(description = "联系方式")
    @ExcelProperty("联系方式")
    private String tel;

    @Schema(description = "期望借用时间")
    @ExcelProperty("期望借用时间")
    private String expectedBorrowTime;

    @Schema(description = "期望归还时间")
    @ExcelProperty("期望归还时间")
    private String expectedReturnTime;

    @Schema(description = "申请时间")
    @ExcelProperty("申请时间")
    private String applyTime;

    @Schema(description = "实际借用时间")
    @ExcelProperty("实际借用时间")
    private String actualBorrowTime;

    @Schema(description = "实际归还时间")
    @ExcelProperty("实际归还时间")
    private String actualReturnTime;

    @Schema(description = "物资借用状态")
    @ExcelProperty(value = "物资借用状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PW_GOODS_BORROW_STATUS)
    private Integer applyStatus;

    @Schema(description = "物品名称")
    @ExcelProperty("物品名称")
    private String goodsName;

    @Schema(description = "借取数量")
    @ExcelProperty("借取数量")
    private Integer borrowNum;

    @Schema(description = "物品备注")
    @ExcelProperty("物品备注")
    private String goodsRemark;

}