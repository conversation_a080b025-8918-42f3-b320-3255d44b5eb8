package cn.iocoder.yudao.module.pw.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 物品导入 Response VO")
@Data
@Builder
public class GoodsImportRespVO {

    @Schema(description = "创建成功的物品名数组", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> createName;

    @Schema(description = "更新成功的物品名数组", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> updateName;

    @Schema(description = "导入失败的物品集合，key 为物品名，value 为失败原因", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, String> failureName;

}
