package cn.iocoder.yudao.module.pw.controller.admin.homepage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/23 0:07
 * @Version 1.0
 */
@Schema(description = "管理后台 - 首页查询返回数据")
@Data
public class HomePageCountListRespVo {

    @Schema(description = "月统计量")
    private List<AccessMonthCountRespVo> accessMonthCountRespVoList;


    @Schema(description = "人员比例分配")
    private List<AccessStudentCountRespVo> accessPersonCountRespVo;


}
