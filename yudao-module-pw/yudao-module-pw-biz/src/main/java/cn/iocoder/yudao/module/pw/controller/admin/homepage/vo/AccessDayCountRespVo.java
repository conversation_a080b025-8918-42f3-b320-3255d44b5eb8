package cn.iocoder.yudao.module.pw.controller.admin.homepage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/24 10:50
 * @Version 1.0
 */
@Schema(description = "管理后台 -日列表数据")
@Data
public class AccessDayCountRespVo {

    @Schema(description = "星期值")
    private String dayOfWeek;

    @Schema(description = "日出去的统计量")
    private String outDayCount;

    @Schema(description = "日进的统计量")
    private String intoDayCount;

    @Schema(description = "区域名称")
    private String pointName;

}
