//package cn.iocoder.yudao.module.pw.dal.mysql.homePage;
//
//import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
//import cn.iocoder.yudao.module.pw.controller.admin.homepage.vo.RoleDO;
//import org.apache.ibatis.annotations.Mapper;
//
//import java.util.ArrayList;
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @Date 2025/5/25 14:07
// * @Version 1.0
// */
//@Mapper
//public interface RoleDOMapper extends BaseMapperX<RoleDO> {
//    /**
//     * 根据用户id查询用户角色类型
//     * @param longs
//     * @return
//     */
//    Map<Long, Integer> selectUserToRoleTypeMapByUserIds(ArrayList<Long> userIds);
//}
