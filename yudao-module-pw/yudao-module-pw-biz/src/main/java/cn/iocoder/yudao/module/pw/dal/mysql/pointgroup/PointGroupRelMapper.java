package cn.iocoder.yudao.module.pw.dal.mysql.pointgroup;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointgroup.PointGroupRelDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 权限分组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointGroupRelMapper extends BaseMapperX<PointGroupRelDO> {
    default List<PointGroupRelDO> selectListByGroupId(Long groupId) {
        return selectList(PointGroupRelDO::getGroupId, groupId);
    }

    default List<PointGroupRelDO> selectListByGroupIds(Collection<Long> groupIds) {
        return selectList(PointGroupRelDO::getGroupId, groupIds);
    }

    default void deleteByGroupIdAndPointIds(Long groupId, Collection<Long> pointIds) {
        delete(new LambdaQueryWrapperX<PointGroupRelDO>()
                .eq(PointGroupRelDO::getGroupId, groupId)
                .in(PointGroupRelDO::getPointId, pointIds));
    }

    default void deleteByGroupId(Long groupId) {
        delete(Wrappers.lambdaUpdate(PointGroupRelDO.class).eq(PointGroupRelDO::getGroupId, groupId));
    }

    default List<Map<String, Object>> selectPointCount(Collection<Long> groupIds) {
        QueryWrapper<PointGroupRelDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.groupBy("group_id")
                .select("group_id AS groupId, COUNT(id) AS count");
        return selectMaps(queryWrapper);
    }
}