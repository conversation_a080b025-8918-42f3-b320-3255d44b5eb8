package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 借用表单新增/修改 Request VO")
@Data
public class BorrowGoodsSaveReqVO {


    @Schema(description = "借用物品关联ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    private Long id;

    @Schema(description = "物品id")
    private Long goodsId;

    @Schema(description = "借取数量")
    private Integer borrowNum;

    @Schema(description = "备注")
    private String remark;


}