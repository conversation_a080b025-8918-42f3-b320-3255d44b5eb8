package cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Set;

@Schema(description = "管理后台 - 人员组 Request VO")
@Data
public class UserGroupSaveReqVO {
    @Schema(description = "组编号", example = "1024")
    private Long id;

    @Schema(description = "组名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotBlank(message = "组名称不能为空")
    @Size(max = 30, message = "组名称长度不能超过 30 个字符")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "用户编号数组", example = "1")
    private Set<Long> userIds;
}
