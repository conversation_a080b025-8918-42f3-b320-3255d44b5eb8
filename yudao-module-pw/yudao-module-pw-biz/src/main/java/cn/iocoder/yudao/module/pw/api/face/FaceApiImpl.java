package cn.iocoder.yudao.module.pw.api.face;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.pw.api.face.dto.FaceReqDTO;
import cn.iocoder.yudao.module.pw.controller.admin.face.vo.FaceSaveReqVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.face.FaceDO;
import cn.iocoder.yudao.module.pw.service.face.FaceService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class FaceApiImpl implements FaceApi {
    @Resource
    private FaceService faceService;

    @Override
    public CommonResult<Boolean> saveFace(FaceReqDTO faceReqDTO) {
        List<FaceDO> faceDOList = faceService.getFaceByUserId(faceReqDTO.getUserId());
        if (CollectionUtils.isAnyEmpty(faceDOList)) {
            faceService.createFace(new FaceSaveReqVO().setFace(faceReqDTO.getFace())
                    .setFaceImage(faceReqDTO.getFaceImage()).setUserId(faceReqDTO.getUserId()));
        } else {
            //一卡一脸
            FaceDO faceDO = faceDOList.get(0);
            faceService.updateFace(new FaceSaveReqVO().setId(faceDO.getId()).setFace(faceReqDTO.getFace())
                    .setFaceImage(faceReqDTO.getFaceImage()).setUserId(faceReqDTO.getUserId()));
        }
        return CommonResult.success(true);
    }
}
