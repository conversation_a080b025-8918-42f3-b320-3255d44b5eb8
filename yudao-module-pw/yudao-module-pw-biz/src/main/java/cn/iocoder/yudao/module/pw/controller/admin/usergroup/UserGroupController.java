package cn.iocoder.yudao.module.pw.controller.admin.usergroup;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo.*;
import cn.iocoder.yudao.module.pw.dal.dataobject.user.UserDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.usergroup.UserGroupDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.usergroup.UserGroupRelDO;
import cn.iocoder.yudao.module.pw.service.user.UserService;
import cn.iocoder.yudao.module.pw.service.usergroup.UserGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 人员分组")
@RestController
@RequestMapping("/pw/user-group")
@Validated
public class UserGroupController {
    @Resource
    private UserGroupService userGroupService;
    @Resource
    private UserService userService;

    @PostMapping("create")
    @Operation(summary = "创建组")
    @PreAuthorize("@ss.hasPermission('pw:user-group:create')")
    public CommonResult<Long> create(@Valid @RequestBody UserGroupSaveReqVO userGroupSaveReqVO) {
        Long id = userGroupService.create(userGroupSaveReqVO);
        return success(id);
    }

    @PutMapping("update")
    @Operation(summary = "更新组")
    @PreAuthorize("@ss.hasPermission('pw:user-group:update')")
    public CommonResult<Boolean> update(@Valid @RequestBody UserGroupSaveReqVO userGroupSaveReqVO) {
        userGroupService.update(userGroupSaveReqVO);
        return success(true);
    }

    @DeleteMapping("delete")
    @Operation(summary = "删除组")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pw:user-group:delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        userGroupService.delete(id);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户分页列表")
    @PreAuthorize("@ss.hasPermission('pw:user-group:query')")
    public CommonResult<PageResult<UserGroupRespVO>> getUserPage(@Valid UserGroupPageReqVO pageReqVO) {
        PageResult<UserGroupDO> pageResult = userGroupService.getUserPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal()));
        }

        Set<Long> ids = pageResult.getList().stream().map(UserGroupDO::getId).collect(Collectors.toSet());
        List<Map<String, Object>> userCount = userGroupService.getUserCount(ids);
        Map<Long, Long> userCountMap = userCount.stream().collect(Collectors.toMap(
                m -> (Long) m.get("groupId"),
                m -> (Long) m.get("count")));

        List<UserGroupRespVO> data = pageResult.getList().stream().map(vo -> {
            UserGroupRespVO userGroupRespVO = BeanUtils.toBean(vo, UserGroupRespVO.class);
            userGroupRespVO.setUserCount(userCountMap.get(vo.getId()));
            return userGroupRespVO;
        }).toList();

        return success(new PageResult<>(data,
                pageResult.getTotal()));
    }

    @GetMapping("/get")
    @Operation(summary = "获得组详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pw:user-group:query')")
    public CommonResult<UserGroupRespVO> get(@RequestParam("id") Long id) {
        UserGroupDO userGroupDO = userGroupService.getUserGroup(id);
        if (userGroupDO == null) {
            return success(null);
        }
        UserGroupRespVO userGroupRespVO = BeanUtils.toBean(userGroupDO, UserGroupRespVO.class);

        List<Long> ids = List.of(id);
        List<Map<String, Object>> userCount = userGroupService.getUserCount(ids);
        Map<Long, Long> pointCountMap = userCount.stream().collect(Collectors.toMap(
                m -> (Long) m.get("groupId"),
                m -> (Long) m.get("count")));

        userGroupRespVO.setUserCount(pointCountMap.get(id));
        List<UserGroupRelDO> userGroupRelDOS = userGroupService.getUserGroupRelDOS(ids);
        if (!CollectionUtils.isAnyEmpty(userGroupRelDOS)) {
            Set<Long> userIds = userGroupRelDOS.stream().map(UserGroupRelDO::getUserId).collect(Collectors.toSet());
            List<UserDO> userDOS = userService.getUsers(userIds);
            if (!CollectionUtils.isAnyEmpty(userDOS)) {
                List<UserRespVO> userRespVOS = userDOS.stream().map(u -> {
                    UserRespVO userRespVO = new UserRespVO();
                    userRespVO.setId(u.getId());
                    userRespVO.setNickname(u.getNickname());
                    userRespVO.setStatus(u.getStatus());
                    userRespVO.setDeptId(u.getDeptId());
                    userRespVO.setGroupId(id);
                    return userRespVO;
                }).toList();

                userGroupRespVO.setUserRespVOS(userRespVOS);
            }
        }
        return success(userGroupRespVO);
    }


    @PostMapping("/import")
    @Operation(summary = "导入用户组")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('pw:user-group:import')")
    public CommonResult<UserGroupImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                           @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<UserGroupImportExcelVO> list = ExcelUtils.read(file, UserGroupImportExcelVO.class);
        return success(userGroupService.importGroup(list));
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得导入用户组模板")
    @PreAuthorize("@ss.hasPermission('pw:user-group:import')")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<UserGroupImportExcelVO> list = Arrays.asList(
                UserGroupImportExcelVO.builder().username("yunai").nickname("123").groupName("CCC").build()
        );
        // 输出
        ExcelUtils.write(response, "用户导入模板.xls", "用户列表", UserGroupImportExcelVO.class, list);
    }

}