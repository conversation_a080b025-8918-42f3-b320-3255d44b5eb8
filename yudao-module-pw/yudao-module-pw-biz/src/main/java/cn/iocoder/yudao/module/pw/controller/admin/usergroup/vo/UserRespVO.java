package cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Schema(description = "管理后台 - 组用户 Response VO")
@Data
public class UserRespVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "用户昵称", example = "小王")
    private String nickname;

    @Schema(description = "帐号状态", example = "1")
    private Integer status; // 参见 CommonStatusEnum 枚举

    @Schema(description = "部门编号", example = "1")
    private Long deptId;

    @Schema(description = "组编号", example = "1")
    private Long groupId;
}
