package cn.iocoder.yudao.module.pw.controller.admin.point.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 点位部署集合 Request VO")
@Data
@ToString(callSuper = true)
public class PointReqVO  {
    @NotNull(message = "区域不能为空")
    @Schema(description = "区域ID")
    private Long regionId;

    @Schema(description = "设备类型ID")
    private boolean showChild;
}