package cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Schema(description = "管理后台 - 人员组 Response VO")
@Data
public class UserGroupRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "用户数量", example = "1")
    private Long userCount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "时间戳格式")
    private LocalDateTime createTime;

    @Schema(description = "用户数组", example = "")
    private List<UserRespVO> userRespVOS = Collections.emptyList();
}
