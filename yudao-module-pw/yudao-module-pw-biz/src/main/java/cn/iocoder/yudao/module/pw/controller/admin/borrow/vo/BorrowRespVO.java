package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 借用申请表单 Response VO")
@Data
public class BorrowRespVO {

    @Schema(description = "物品借用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "申请人ID")
    private Long applyId;

    @Schema(description = "借用人名称")
    private String applyName;

    @Schema(description = "联系方式")
    private String tel;

    @Schema(description = "期望借用时间")
    private String expectedBorrowTime;

    @Schema(description = "期望归还时间")
    private String expectedReturnTime;

    @Schema(description = "申请时间")
    private String applyTime;

    @Schema(description = "实际借用时间")
    private String actualBorrowTime;

    @Schema(description = "实际归还时间")
    private String actualReturnTime;

    @Schema(description = "物资借用状态")
    private Integer applyStatus;

    @Schema(description = "借用表单图片列表")
    private List<BorrowImageRespVO> borrowImageRespVOS;
    @Schema(description = "借用物品列表")
    private List<BorrowGoodsRespVO> borrowGoodsRespVOS;
    @Schema(description = "审批列表")
    private List<BorrowProcessRespVO> borrowProcessRespVOS;


}