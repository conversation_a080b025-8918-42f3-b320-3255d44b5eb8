package cn.iocoder.yudao.module.pw.controller.admin.roomappointment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 预约记录审批 Request VO")
@Data
@ToString(callSuper = true)
public class RoomAppointmentExamineReqVO{

    @Schema(description = "主键")
    private Long id;
    @Schema(description = "0：待审核；1：审核通过；2：审核拒绝")
    private Integer reviewStatus;
}