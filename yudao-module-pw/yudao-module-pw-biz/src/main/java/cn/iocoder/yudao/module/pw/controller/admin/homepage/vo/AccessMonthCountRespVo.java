package cn.iocoder.yudao.module.pw.controller.admin.homepage.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/24 11:12
 * @Version 1.0
 */
@Schema(description = "管理后台 -月列表数据")
@Data
public class AccessMonthCountRespVo {

    @Schema(description = "月份出去的统计量")
    private Integer outMonthCount;

    @Schema(description = "月份进的统计量")
    private Integer intoMonthCount;

    @Schema(description = "月份")
    private Integer month;
}
