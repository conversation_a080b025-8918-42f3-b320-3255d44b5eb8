package cn.iocoder.yudao.module.pw.dal.mysql.borrow;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.controller.admin.borrow.vo.BorrowPageReqVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.borrow.BorrowDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;

@Mapper
public interface BorrowMapper extends BaseMapperX<BorrowDO> {

    default PageResult<BorrowDO> selectPage(BorrowPageReqVO pageReqVO, Collection<Long> borrowIds,Long applyId) {

        LambdaQueryWrapperX<BorrowDO> wrapperX = new LambdaQueryWrapperX<BorrowDO>()
                .inIfPresent(BorrowDO::getApplyStatus, pageReqVO.getApplyStatus())
                .eqIfPresent(BorrowDO::getApplyId, applyId)
                .orderByDesc(BorrowDO::getId);

        if (pageReqVO.getExpectedBorrowTime() != null && pageReqVO.getExpectedBorrowTime().length == 2) {
            wrapperX.between(BorrowDO::getExpectedBorrowTime, DateUtil.parseLocalDateTime(pageReqVO.getExpectedBorrowTime()[0])
                    , DateUtil.parseLocalDateTime(pageReqVO.getExpectedBorrowTime()[1]));
        }
        if (pageReqVO.getExpectedReturnTime() != null && pageReqVO.getExpectedReturnTime().length == 2) {
            wrapperX.between(BorrowDO::getExpectedReturnTime, DateUtil.parseLocalDateTime(pageReqVO.getExpectedReturnTime()[0])
                    , DateUtil.parseLocalDateTime(pageReqVO.getExpectedReturnTime()[1]));
        }
        if (!CollectionUtils.isAnyEmpty(borrowIds)) {
            wrapperX.in(BorrowDO::getId, borrowIds);
        }
        return selectPage(pageReqVO, wrapperX);


    }
}
