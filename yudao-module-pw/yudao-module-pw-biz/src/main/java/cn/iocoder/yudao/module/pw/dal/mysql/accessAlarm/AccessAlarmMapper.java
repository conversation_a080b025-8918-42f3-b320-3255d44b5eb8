package cn.iocoder.yudao.module.pw.dal.mysql.accessAlarm;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.controller.admin.alarm.Vo.AccessAlarmPageReqVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.accessalarm.AccessAlarmDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccessAlarmMapper extends BaseMapperX<AccessAlarmDO> {

    default PageResult<AccessAlarmDO> selectPage(AccessAlarmPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccessAlarmDO>()
                .likeIfPresent(AccessAlarmDO::getName, reqVO.getName())
                .likeIfPresent(AccessAlarmDO::getUserName, reqVO.getUserName())
                .likeIfPresent(AccessAlarmDO::getAlarmName, reqVO.getAlarmName())
                .betweenIfPresent(AccessAlarmDO::getLogDatetime, reqVO.getLogDatetime())
                .eqIfPresent(AccessAlarmDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(AccessAlarmDO::getDueDate, reqVO.getDueDate())
                .eqIfPresent(AccessAlarmDO::getCollegeType, reqVO.getCollegeType())
                .likeIfPresent(AccessAlarmDO::getCollegeName, reqVO.getCollegeName())
                .eqIfPresent(AccessAlarmDO::getAlarmType, reqVO.getAlarmType())
                .betweenIfPresent(AccessAlarmDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AccessAlarmDO::getId));
    }
    // 查询方法
    default PageResult<AccessAlarmDO> selectAbsentToday(LocalDateTime todayStart, LocalDateTime todayEnd,Integer pageNo,Integer pageSize) {
        // 创建分页查询条件
        LambdaQueryWrapper<AccessAlarmDO> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        queryWrapper
                .ge(AccessAlarmDO::getLogDatetime, todayStart)      // 大于等于开始时间
                .lt(AccessAlarmDO::getLogDatetime, todayEnd)        // 小于结束时间
                .eq(AccessAlarmDO::getAlarmType, 2)                 // 告警类型为2（未归）
                .eq(AccessAlarmDO::getDeleted, 0);                  // 未删除记录

        // 添加排序条件（按ID倒序）
        queryWrapper.orderByDesc(AccessAlarmDO::getId);

        // 执行分页查询（假设当前页码为1，每页10条记录）
        Page<AccessAlarmDO> page = new Page<>(pageNo, pageSize);
        Page<AccessAlarmDO> resultPage = selectPage(page, queryWrapper);

        // 转换为自定义分页结果对象
        return new PageResult<>(resultPage.getRecords(), resultPage.getTotal());
    }



}