package cn.iocoder.yudao.module.pw.dal.mysql.usergroup;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo.UserGroupPageReqVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointgroup.PointGroupRelDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.usergroup.UserGroupDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 人员组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserGroupMapper extends BaseMapperX<UserGroupDO> {

    default PageResult<UserGroupDO> selectPage(UserGroupPageReqVO groupUserPageReqVO) {
        LambdaQueryWrapperX<UserGroupDO> queryWrapper = new LambdaQueryWrapperX<>();
        if (groupUserPageReqVO.getName() != null) {
            queryWrapper.like(UserGroupDO::getName, groupUserPageReqVO.getName());
        }
        return selectPage(groupUserPageReqVO, queryWrapper .orderByDesc(UserGroupDO::getId));
    }


}