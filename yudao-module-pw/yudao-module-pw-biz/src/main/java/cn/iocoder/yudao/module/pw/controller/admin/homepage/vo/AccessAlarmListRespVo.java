package cn.iocoder.yudao.module.pw.controller.admin.homepage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/24 12:06
 * @Version 1.0
 */
@Schema(description = "管理后台 - 告警信息返回列表")
@Data
public class AccessAlarmListRespVo {

    @Schema(description = "学生姓名")
   private String name;

    @Schema(description = "学工号")
    private String userName;

    @Schema(description = "类型")
    private String alarmType;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "时间")
    private String dueDate;

    @Schema(description = "学院")
    private String  collegeName;

    @Schema(description = "类型")
    private String alarmTypeName;




}
