package cn.iocoder.yudao.module.pw.dal.mysql.roleregion;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pw.dal.dataobject.roleregion.RoleRegionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 角色区域权限 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RoleRegionMapper extends BaseMapperX<RoleRegionDO> {

    default List<RoleRegionDO> selectListByRoleId(Collection<Long> roleIds) {
        return selectList(RoleRegionDO::getRoleId, roleIds);
    }

}