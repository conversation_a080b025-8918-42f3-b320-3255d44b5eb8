package cn.iocoder.yudao.module.pw.api.face;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pw.api.face.dto.UserDevicePermissionReqDTO;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointrule.PointRuleDO;
import cn.iocoder.yudao.module.pw.dal.mysql.pointrule.PointRuleMapper;
import cn.iocoder.yudao.module.pw.service.pointpermission.PointPermissionAsyncService;
import cn.iocoder.yudao.module.pw.service.pointpermission.PointPermissionService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class UserDevicePermissionApiImpl implements UserDevicePermissionApi {


    @Resource
    private PointPermissionService pointPermissionService;

    @Resource
    private PointRuleMapper pointRuleMapper;

    @Resource
    private PointPermissionAsyncService pointPermissionAsyncService;

    @Override
    public CommonResult<Boolean> userEvent(UserDevicePermissionReqDTO userDevicePermissionReqDTO) {
        if(userDevicePermissionReqDTO==null){
            return CommonResult.success(false);
        }
        if(userDevicePermissionReqDTO.getRegionId()!=null){
            pointPermissionService.setUserPermission(userDevicePermissionReqDTO.getUserId(), userDevicePermissionReqDTO.getRegionId(),
                    userDevicePermissionReqDTO.getAction());
        }
        if(userDevicePermissionReqDTO.getDeptId()!=null){
            List<PointRuleDO> pointRuleDOS = pointRuleMapper.selectList(new QueryWrapper<PointRuleDO>().lambda().eq(PointRuleDO::getUserId, userDevicePermissionReqDTO.getDeptId()).eq(PointRuleDO::getUserType, 0));
            pointPermissionAsyncService.save4user(pointRuleDOS,List.of(userDevicePermissionReqDTO.getUserId()),userDevicePermissionReqDTO.getAction());
        }

        return CommonResult.success(true);
    }




}
