package cn.iocoder.yudao.module.pw.controller.admin.homepage.vo;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.point.PointDO;
import cn.iocoder.yudao.module.pw.dal.dataobject.user.UserDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/5/24 20:13
 * @Version 1.0
 */
@Data
public class CallbackLogVo extends BaseDO {

    private Long id;
    private Long logUserId;
    private UserDO logUser;
    private Long logPointId;
    private PointDO logPoint;
    private Long logDeviceId;
    private LocalDateTime logDatetime;
    private String logInfo;
    private String deviceName;
    private String pointName;
}
