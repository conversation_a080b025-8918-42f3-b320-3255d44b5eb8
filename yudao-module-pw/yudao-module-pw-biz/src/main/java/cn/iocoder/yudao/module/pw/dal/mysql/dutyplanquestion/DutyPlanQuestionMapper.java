package cn.iocoder.yudao.module.pw.dal.mysql.dutyplanquestion;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pw.dal.dataobject.dutyplanquestion.DutyPlanQuestionDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.pw.controller.admin.dutyplanquestion.vo.*;

/**
 * 值班计划问卷回答 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DutyPlanQuestionMapper extends BaseMapperX<DutyPlanQuestionDO> {

    default PageResult<DutyPlanQuestionDO> selectPage(DutyPlanQuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DutyPlanQuestionDO>()
                .eqIfPresent(DutyPlanQuestionDO::getDutyPlanId, reqVO.getDutyPlanId())
                .eqIfPresent(DutyPlanQuestionDO::getQuestionAnswer, reqVO.getQuestionAnswer())
                .betweenIfPresent(DutyPlanQuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DutyPlanQuestionDO::getId));
    }

}