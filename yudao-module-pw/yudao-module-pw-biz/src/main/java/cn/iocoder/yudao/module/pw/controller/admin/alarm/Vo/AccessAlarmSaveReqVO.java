package cn.iocoder.yudao.module.pw.controller.admin.alarm.Vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 告警信息新增/修改 Request VO")
@Data
public class AccessAlarmSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20904")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String name;

    @Schema(description = "学工号", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String userName;

    @Schema(description = "告警名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String alarmName;

    @Schema(description = "事件记录时间")
    private LocalDateTime logDatetime;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String remark;

    @Schema(description = "日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dueDate;

    @Schema(description = "学院类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer collegeType;

    @Schema(description = "学院", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String collegeName;

    @Schema(description = "告警类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer alarmType;

}