package cn.iocoder.yudao.module.pw.controller.admin.homepage;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pw.controller.admin.alarm.Vo.AccessAlarmRespVO;
import cn.iocoder.yudao.module.pw.controller.admin.homepage.vo.*;
import cn.iocoder.yudao.module.pw.service.homePage.HomePageAccessCountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @Date 2025/5/22 23:13
 * @Version 1.0
 */
@Tag(name = "管理后台 - 首页")
@RestController
@RequestMapping("/pw/home-page")
@Validated
public class HomePageController {

    @Resource
    private HomePageAccessCountService homePageAccessCountService;

    @GetMapping("/count")
    @Operation(summary = "首页统计")
    @PreAuthorize("@ss.hasPermission('pw:home-page:count')")
    public CommonResult<AccessTotalCountRespVo> queryStudentAccessCount() {
        HomePageCountReqVo countReqVo = new HomePageCountReqVo();
        AccessTotalCountRespVo accessTotalCountRespVo = homePageAccessCountService.queryStudentAccessCountV2(countReqVo);
        return success(BeanUtils.toBean(accessTotalCountRespVo, AccessTotalCountRespVo.class));
    }


    @GetMapping("/month-count")
    @Operation(summary = "首页月统计")
    @PreAuthorize("@ss.hasPermission('pw:home-page:month-count')")
    public CommonResult<HomePageCountListRespVo> queryMonthAccessCount() {
        HomePageCountReqVo countReqVo = new HomePageCountReqVo();
        HomePageCountListRespVo respVo = homePageAccessCountService.queryMonthAccessCount(countReqVo);
        return success(BeanUtils.toBean(respVo, HomePageCountListRespVo.class));
    }

    @GetMapping("/day-count")
    @Operation(summary = "首页重点出入口流量统计")
    @PreAuthorize("@ss.hasPermission('pw:home-page:day-count')")
    public CommonResult<List<AccessDayCountRespVo>> queryDayAccessCount() {
        HomePageCountReqVo countReqVo = new HomePageCountReqVo();
        List<AccessDayCountRespVo> respVoList = homePageAccessCountService.queryDayAccessCount(countReqVo);
        return success(BeanUtils.toBean(respVoList, AccessDayCountRespVo.class));
    }


    @GetMapping("/alarm-list")
    @Operation(summary = "告警信息列表")
    @PreAuthorize("@ss.hasPermission('pw:home-page:alarm-list')")
    public CommonResult<PageResult<AccessAlarmListRespVo>> queryAlarmList() {
        PageResult<AccessAlarmRespVO> pageResult = homePageAccessCountService.queryAlarmListV2();
        return success(BeanUtils.toBean(pageResult, AccessAlarmListRespVo.class));
    }

    @PostMapping("/test-full")
    @Operation(summary = "")
    @PreAuthorize("@ss.hasPermission('pw:home-page:test-full')")
    public CommonResult<PageResult<AccessAlarmListRespVo>> testFull() {
        homePageAccessCountService.fullCalculateAccessAlarm();
        return success(PageResult.empty());
    }


    @PostMapping("/test-stock")
    @Operation(summary = "告警信息列表")
    @PreAuthorize("@ss.hasPermission('pw:home-page:test-stock')")
    public CommonResult<PageResult<AccessAlarmListRespVo>> testStock() {
        homePageAccessCountService.stockCalculateAccessAlarm();
        return success(PageResult.empty());
    }

}
