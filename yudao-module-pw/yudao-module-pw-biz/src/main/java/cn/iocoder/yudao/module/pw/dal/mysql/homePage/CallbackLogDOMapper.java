package cn.iocoder.yudao.module.pw.dal.mysql.homePage;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pw.controller.admin.homepage.vo.CallbackLogVo;
import cn.iocoder.yudao.module.pw.dal.dataobject.user.UserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/24 20:19
 * @Version 1.0
 */
@Mapper
public interface CallbackLogDOMapper extends BaseMapperX<UserDO> {

    /**
     * `
     * @param startDate
     * @param endDate
     * @return
     */
    List<CallbackLogVo> getLogsBetweenDates(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 统计当日未归人数
     * @return
     */
    Long countAbsentPersons();
    /**
     * 统计指定时间段未归人数
     * @param startDate
     * @param endDate
     * @return
     */
    Long countAbsentPersonsByDays(@Param("startDate")String startDate,@Param("endDate") String endDate);

    List<CallbackLogVo> getLogsOfYear();

    List<CallbackLogVo> getLogsOfCurrentWeek(LocalDateTime targetDate);

    List<CallbackLogVo> selectPageForAccessAlarm(int pageNum, int pageSize);

    List<CallbackLogVo> queryAccessAlarmList();
}
