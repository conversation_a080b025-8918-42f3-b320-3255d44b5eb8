package cn.iocoder.yudao.module.pw.dal.mysql.pointrule;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.controller.admin.pointrule.vo.PointRulePageReqVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointrule.PointRuleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;

@Mapper
public interface PointRuleMapper extends BaseMapperX<PointRuleDO> {
    default PointRuleDO select(Long userId, Integer userType, Long pointId, Integer pointType) {
        LambdaQueryWrapperX<PointRuleDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(PointRuleDO::getUserId, userId);
        queryWrapper.eq(PointRuleDO::getUserType, userType);
        queryWrapper.eq(PointRuleDO::getPointId, pointId);
        queryWrapper.eq(PointRuleDO::getPointType, pointType);
        return selectOne(queryWrapper);
    }


    default PageResult<PointRuleDO> selectPage(PointRulePageReqVO reqVO, Collection<Long> userIds, Collection<Long> pointIds) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PointRuleDO>()
                .inIfPresent(PointRuleDO::getUserId, userIds)
                .inIfPresent(PointRuleDO::getPointId, pointIds)
                .orderByDesc(PointRuleDO::getId));
    }
}
