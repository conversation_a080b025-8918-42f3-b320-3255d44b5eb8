package cn.iocoder.yudao.module.pw.dal.mysql.pointgroup;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pw.controller.admin.pointgroup.vo.PointGroupPageReqVO;
import cn.iocoder.yudao.module.pw.dal.dataobject.pointgroup.PointGroupDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 权限分组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PointGroupMapper extends BaseMapperX<PointGroupDO> {

    default PageResult<PointGroupDO> selectPage(PointGroupPageReqVO pointGroupPageReqVO) {
        LambdaQueryWrapperX<PointGroupDO> queryWrapper = new LambdaQueryWrapperX<>();
        if (pointGroupPageReqVO.getName() != null) {
            queryWrapper.like(PointGroupDO::getName, pointGroupPageReqVO.getName());
        }
        return selectPage(pointGroupPageReqVO, queryWrapper .orderByDesc(PointGroupDO::getId));
    }
}