package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 借用流程表单新增/修改 Request VO")
@Data
public class BorrowProcessSaveReqVO {

    @Schema(description = "借用流程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    private Long id;

    @Schema(description = "借用表单id")
    private Long borrowId;

    @Schema(description = "意见")
    private String remark;

    @Schema(description = "表单图片列表")
    private List<BorrowImageSaveReqVO> borrowImageSaveReqVOS;

    @Schema(description = "审批状态")
    private Integer applyStatus;


}