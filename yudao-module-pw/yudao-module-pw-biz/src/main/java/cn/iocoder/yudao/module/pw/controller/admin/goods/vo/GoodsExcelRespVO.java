package cn.iocoder.yudao.module.pw.controller.admin.goods.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 物品 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GoodsExcelRespVO {

    @Schema(description = "物品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    @ExcelProperty("物品ID")
    private Long id;

    @Schema(description = "物品名称")
    @ExcelProperty("物品名称")
    private String name;

    @Schema(description = "物品简介")
    @ExcelProperty("物品简介")
    private String goodsDesc;

    @Schema(description = "库存数量")
    @ExcelProperty("库存数量")
    private Integer amount;

    @Schema(description = "物品单位")
    @ExcelProperty("物品单位")
    private String unit;

    @Schema(description = "物品状态")
    @ExcelProperty(value = "物品状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

}