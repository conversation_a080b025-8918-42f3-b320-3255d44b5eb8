package cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class UserGroupImportExcelVO {

    @ExcelProperty("工号")
    private String username;

    @ExcelProperty("姓名")
    private String nickname;

    @ExcelProperty("楼栋")
    private String groupName;



}
