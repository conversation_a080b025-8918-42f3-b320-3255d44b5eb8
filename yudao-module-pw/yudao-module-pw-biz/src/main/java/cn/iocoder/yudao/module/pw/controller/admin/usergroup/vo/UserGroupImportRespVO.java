package cn.iocoder.yudao.module.pw.controller.admin.usergroup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 用户导入 Response VO")
@Data
@Builder
public class UserGroupImportRespVO {

    @Schema(description = "创建成功的用户组名", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> createName;

    @Schema(description = "更新成功的用户组名", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> updateName;

    @Schema(description = "导入失败的用户组名集合，key 为用户名，value 为失败原因", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, String> failureName;

}
