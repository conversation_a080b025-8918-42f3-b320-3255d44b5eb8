package cn.iocoder.yudao.module.pw.controller.admin.homepage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/5/24 12:00
 * @Version 1.0
 */
@Schema(description = "管理后台 - 学生类型比例返回数据")
@Data
public class AccessStudentCountRespVo {


    @Schema(description = "学生类型")
    private Integer studentType;
    @Schema(description = "学生类型")
    private String studentTypeName;

    @Schema(description = "学生类型占比")
    private String studentTypeRatio;
}
