package cn.iocoder.yudao.module.pw.controller.admin.borrow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 借用申请表单图片 Response VO")
@Data
public class BorrowImageRespVO {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16532")
    private Long id;

    @Schema(description = "借用申请表单ID")
    private Long borrowId;

    @Schema(description = "借用状态")
    private Integer applyStatus;

    @Schema(description = "图片地址")
    private String image;

    @Schema(description = "备注")
    private String remark;


}