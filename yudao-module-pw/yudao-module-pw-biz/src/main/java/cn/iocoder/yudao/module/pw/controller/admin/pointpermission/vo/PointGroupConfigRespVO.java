package cn.iocoder.yudao.module.pw.controller.admin.pointpermission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 人员点位权限 Resp VO")
@Data
@ToString(callSuper = true)
public class PointGroupConfigRespVO {
    private Long id;

    @Schema(description = "点位ID")
    private Long pointId;

    @Schema(description = "人员组ID")
    private Long userGroupId;

    @Schema(description = "人员组名称")
    private String userGroupName;

    @Schema(description = "1：长期有效；2：自定义有效期")
    private Integer expired;

    @Schema(description = "权限开始时间")
    private String expiredBegin;

    @Schema(description = "权限结束时间")
    private String expiredEnd;
}
