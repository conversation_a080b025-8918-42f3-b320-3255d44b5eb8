package com.xxl.job.admin.core.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sys_schedule_job_log")
public class ScheduleJobLog {
    private Long id;
    private Long jobId;           // 任务ID
    private String handlerName;   // 处理器名字
    private Date executeTime;     // 执行时间
    private String executeParam;  // 执行参数
    private Integer executeStatus;// 执行状态：0-执行中 1-成功 2-失败
    private String executeResult; // 执行结果
    private String errorMsg;      // 错误信息
    private Integer duration;     // 执行耗时（毫秒）
    private Date createTime;      // 创建时间
}
