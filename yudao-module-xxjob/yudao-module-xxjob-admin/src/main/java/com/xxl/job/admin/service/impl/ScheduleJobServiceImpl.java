package com.xxl.job.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.admin.core.model.ScheduleJob;
import com.xxl.job.admin.dao.ScheduleJobMapper;
import com.xxl.job.admin.service.ScheduleJobService;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.baomidou.mybatisplus.extension.toolkit.Db.save;

// 定时任务Service实现
@Service
public class ScheduleJobServiceImpl extends ServiceImpl<ScheduleJobMapper, ScheduleJob>
                                     implements ScheduleJobService {

    @Override
    public Long addJob(ScheduleJob job) {
        save(job);
        return job.getId();
    }

    @Override
    public List<ScheduleJob> listJobs(String jobName, Integer status, String handlerName) {
        LambdaQueryWrapper<ScheduleJob> wrapper = new LambdaQueryWrapper<>();
        if (jobName != null) wrapper.like(ScheduleJob::getJobName, jobName);
        if (status != null) wrapper.eq(ScheduleJob::getStatus, status);
        if (handlerName != null) wrapper.like(ScheduleJob::getHandlerName, handlerName);
        return list(wrapper);
    }

    @Override
    public List<ScheduleJob> exportJobs(String jobName, Integer status, String handlerName) {
        return listJobs(jobName, status, handlerName);
    }

    @Override
    public void updateJobStatus(Long jobId, Integer status) {
        ScheduleJob job = getById(jobId);
        if (job != null) {
            job.setStatus(status);
            updateById(job);
        }
    }
}