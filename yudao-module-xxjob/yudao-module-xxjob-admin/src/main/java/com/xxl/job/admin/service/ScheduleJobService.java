package com.xxl.job.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xxl.job.admin.core.model.ScheduleJob;

import java.util.List;

// 定时任务Service接口
public interface ScheduleJobService extends IService<ScheduleJob> {
    Long addJob(ScheduleJob job);
    List<ScheduleJob> listJobs(String jobName, Integer status, String handlerName);
    List<ScheduleJob> exportJobs(String jobName, Integer status, String handlerName);
    void updateJobStatus(Long jobId, Integer status);
}