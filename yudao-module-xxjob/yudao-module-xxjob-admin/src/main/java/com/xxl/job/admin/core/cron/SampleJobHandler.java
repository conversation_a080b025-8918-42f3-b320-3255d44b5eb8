package com.xxl.job.admin.core.cron;

import com.xxl.job.admin.core.model.ScheduleJob;
import com.xxl.job.admin.service.ScheduleJobLogService;
import com.xxl.job.admin.service.ScheduleJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

@Component
public class SampleJobHandler {

    private final ScheduleJobService scheduleJobService;
    private final ScheduleJobLogService jobLogService;

    public SampleJobHandler(ScheduleJobService scheduleJobService, 
                           ScheduleJobLogService jobLogService) {
        this.scheduleJobService = scheduleJobService;
        this.jobLogService = jobLogService;
    }

    @XxlJob("xxl-job-testJob")
    public void execute() {
        String handlerName = "Job";
        String param = XxlJobHelper.getJobParam();
        // 查询任务信息
        ScheduleJob job = scheduleJobService.listJobs(null, null, handlerName).get(0);
        Long jobId = job.getId();
        // 记录任务开始
        Long logId = jobLogService.recordJobStart(jobId, handlerName, param);
        long startTime = System.currentTimeMillis();
        try {
            // 业务逻辑处理
            XxlJobHelper.log("任务执行中，参数：{}", param);
            // 模拟业务处理
            Thread.sleep(2000);
            // 记录任务成功
            String result = "处理完成，参数：" + param;
            jobLogService.recordJobSuccess(logId, result, 
                    (int) (System.currentTimeMillis() - startTime));
            
            XxlJobHelper.handleSuccess(); // 标记成功
        } catch (Exception e) {
            // 记录任务失败
            jobLogService.recordJobFailure(logId, e.getMessage(), 
                    (int) (System.currentTimeMillis() - startTime));
            XxlJobHelper.handleFail("任务执行失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}