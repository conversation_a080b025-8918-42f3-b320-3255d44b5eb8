package com.xxl.job.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xxl.job.admin.core.model.JobExecutionStats;
import com.xxl.job.admin.core.model.ScheduleJobLog;

import java.util.Date;
import java.util.List;

// 定时任务日志Service接口
public interface ScheduleJobLogService extends IService<ScheduleJobLog> {
    Long recordJobStart(Long jobId, String handlerName, String executeParam);
    void recordJobSuccess(Long logId, String executeResult, Integer duration);
    void recordJobFailure(Long logId, String errorMsg, Integer duration);
    List<ScheduleJobLog> listJobLogs(Long jobId, String handlerName, Integer executeStatus,
                                     Date startTime, Date endTime, Integer page, Integer size);
    JobExecutionStats getJobExecutionStats(Long jobId);
}
