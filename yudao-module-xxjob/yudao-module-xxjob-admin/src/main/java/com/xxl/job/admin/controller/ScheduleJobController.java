package com.xxl.job.admin.controller;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xxl.job.admin.core.model.JobExecutionStats;
import com.xxl.job.admin.core.model.ScheduleJob;
import com.xxl.job.admin.core.model.ScheduleJobLog;
import com.xxl.job.admin.service.ScheduleJobLogService;
import com.xxl.job.admin.service.ScheduleJobService;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

// 定时任务控制器
@RestController
@RequestMapping("/api/schedule-job")
public class ScheduleJobController {

    private final ScheduleJobService scheduleJobService;
    private final ScheduleJobLogService scheduleJobLogService;

    public ScheduleJobController(ScheduleJobService scheduleJobService, 
                               ScheduleJobLogService scheduleJobLogService) {
        this.scheduleJobService = scheduleJobService;
        this.scheduleJobLogService = scheduleJobLogService;
    }

    // ========== 任务管理接口 ==========
    @PostMapping("/add")
    public CommonResult<Long> addJob(@RequestBody ScheduleJob job) {
        Long jobId = scheduleJobService.addJob(job);
        return CommonResult.success(jobId);
    }

    @GetMapping("/list")
    public CommonResult<PageResult<ScheduleJob>> listJobs(
            @RequestParam(required = false) String jobName,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String handlerName,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize
    ) {
        // 创建分页查询条件
        Page<ScheduleJob> page = new Page<>(pageNum, pageSize);
        // 执行分页查询
        LambdaQueryWrapper<ScheduleJob> wrapper = new LambdaQueryWrapper<>();
        if (jobName != null) wrapper.like(ScheduleJob::getJobName, jobName);
        if (status != null) wrapper.eq(ScheduleJob::getStatus, status);
        if (handlerName != null) wrapper.like(ScheduleJob::getHandlerName, handlerName);

        IPage<ScheduleJob> jobPage = scheduleJobService.page(page, wrapper);
        // 构建分页结果
        PageResult<ScheduleJob> pageResult = new PageResult<>();
        pageResult.setTotal(jobPage.getTotal());
        pageResult.setList(jobPage.getRecords());
        return CommonResult.success(pageResult);
    }


    @DeleteMapping("/delete/{jobId}")
    public CommonResult<Void> deleteJob(@PathVariable Long jobId) {
       // scheduleJobService.removeById(jobId);

        // 物理删除（可选，需谨慎）
         scheduleJobService.removeById(jobId);

        return CommonResult.success(null);
    }

    @PutMapping("/update")
    public CommonResult<Void> updateJob(@RequestBody ScheduleJob job) {
        if (job.getId() == null) {
            return CommonResult.error(500,"任务ID不能为空");
        }

        // 确保重要字段不被空值覆盖
        ScheduleJob oldJob = scheduleJobService.getById(job.getId());
        if (oldJob == null) {
            return CommonResult.error(500,"任务不存在");
        }
        // 选择性更新字段（根据业务需求调整）
        if (job.getJobName() != null) oldJob.setJobName(job.getJobName());
        if (job.getHandlerName() != null) oldJob.setHandlerName(job.getHandlerName());
        if (job.getHandlerParam() != null) oldJob.setHandlerParam(job.getHandlerParam());
        if (job.getCronExpression() != null) oldJob.setCronExpression(job.getCronExpression());
        if (job.getRetryCount() != null) oldJob.setRetryCount(job.getRetryCount());
        if (job.getRetryInterval() != null) oldJob.setRetryInterval(job.getRetryInterval());
        if (job.getTimeout() != null) oldJob.setTimeout(job.getTimeout());
        if (job.getStatus() != null) oldJob.setStatus(job.getStatus());
        scheduleJobService.updateById(oldJob);
        return CommonResult.success(null);
    }


    @PostMapping("/update-status/{jobId}/{status}")
    public CommonResult<Void> updateJobStatus(
            @PathVariable Long jobId,
            @PathVariable Integer status
    ) {
        scheduleJobService.updateJobStatus(jobId, status);
        return CommonResult.success(null);
    }

    // ========== 任务日志接口 ==========
    @GetMapping("/log/list")
    public CommonResult<List<ScheduleJobLog>> listJobLogs(
            @RequestParam(required = false) Long jobId,
            @RequestParam(required = false) String handlerName,
            @RequestParam(required = false) Integer executeStatus,
            @RequestParam(required = false) Date startTime,
            @RequestParam(required = false) Date endTime,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer size
    ) {
        List<ScheduleJobLog> logs = scheduleJobLogService.listJobLogs(
                jobId, handlerName, executeStatus, startTime, endTime, page, size);
        return CommonResult.success(logs);
    }

    @GetMapping("/log/stats")
    public CommonResult<JobExecutionStats> getJobExecutionStats(
            @RequestParam Long jobId
    ) {
        JobExecutionStats stats = scheduleJobLogService.getJobExecutionStats(jobId);
        return CommonResult.success(stats);
    }
}