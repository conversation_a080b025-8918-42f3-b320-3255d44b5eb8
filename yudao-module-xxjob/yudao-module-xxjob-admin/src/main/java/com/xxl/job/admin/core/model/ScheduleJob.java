package com.xxl.job.admin.core.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sys_schedule_job")
public class ScheduleJob {
    private Long id;
    private String jobName;         // 任务名称
    private String handlerName;     // 处理器名字
    private String handlerParam;    // 处理器参数
    private String cronExpression;  // CRON表达式
    private Integer retryCount;     // 重试次数
    private Integer retryInterval;  // 重试间隔（毫秒）
    private Integer timeout;        // 监控超时时间（毫秒）
    private Integer status;         // 任务状态：1-启用 2-暂停
    private Date createTime;        // 创建时间
    private Date updateTime;        // 更新时间
}