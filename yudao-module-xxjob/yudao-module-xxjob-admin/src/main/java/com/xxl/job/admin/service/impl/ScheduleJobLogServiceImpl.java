package com.xxl.job.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.admin.core.model.JobExecutionStats;
import com.xxl.job.admin.core.model.ScheduleJobLog;
import com.xxl.job.admin.dao.ScheduleJobLogMapper;
import com.xxl.job.admin.service.ScheduleJobLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

// 定时任务日志Service实现
@Service
public class ScheduleJobLogServiceImpl extends ServiceImpl<ScheduleJobLogMapper, ScheduleJobLog>
                                      implements ScheduleJobLogService {

    @Override
    @Transactional
    public Long recordJobStart(Long jobId, String handlerName, String executeParam) {
        ScheduleJobLog log = new ScheduleJobLog();
        log.setJobId(jobId);
        log.setHandlerName(handlerName);
        log.setExecuteParam(executeParam);
        log.setExecuteTime(new Date());
        log.setExecuteStatus(0); // 执行中
        save(log);
        return log.getId();
    }

    @Override
    @Transactional
    public void recordJobSuccess(Long logId, String executeResult, Integer duration) {
        ScheduleJobLog log = getById(logId);
        if (log != null) {
            log.setExecuteStatus(1); // 成功
            log.setExecuteResult(executeResult);
            log.setDuration(duration);
            updateById(log);
        }
    }

    @Override
    @Transactional
    public void recordJobFailure(Long logId, String errorMsg, Integer duration) {
        ScheduleJobLog log = getById(logId);
        if (log != null) {
            log.setExecuteStatus(2); // 失败
            log.setErrorMsg(errorMsg);
            log.setDuration(duration);
            updateById(log);
        }
    }

    @Override
    public List<ScheduleJobLog> listJobLogs(Long jobId, String handlerName, Integer executeStatus,
                                            Date startTime, Date endTime, Integer page, Integer size) {
        LambdaQueryWrapper<ScheduleJobLog> wrapper = new LambdaQueryWrapper<>();
        if (jobId != null) wrapper.eq(ScheduleJobLog::getJobId, jobId);
        if (handlerName != null) wrapper.like(ScheduleJobLog::getHandlerName, handlerName);
        if (executeStatus != null) wrapper.eq(ScheduleJobLog::getExecuteStatus, executeStatus);
        if (startTime != null) wrapper.ge(ScheduleJobLog::getExecuteTime, startTime);
        if (endTime != null) wrapper.le(ScheduleJobLog::getExecuteTime, endTime);
        wrapper.orderByDesc(ScheduleJobLog::getExecuteTime);

        // 分页处理
        if (page != null && size != null) {
            return page(new Page<>(page, size), wrapper).getRecords();
        }

        return list(wrapper);
    }

    @Override
    public JobExecutionStats getJobExecutionStats(Long jobId) {
        long successCount = count(new LambdaQueryWrapper<ScheduleJobLog>()
                .eq(ScheduleJobLog::getJobId, jobId)
                .eq(ScheduleJobLog::getExecuteStatus, 1));

         long failureCount = count(new LambdaQueryWrapper<ScheduleJobLog>()
                .eq(ScheduleJobLog::getJobId, jobId)
                .eq(ScheduleJobLog::getExecuteStatus, 2));

        long totalCount = successCount + failureCount;
        double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;

        return new JobExecutionStats(successCount, failureCount, totalCount, successRate);
    }
}