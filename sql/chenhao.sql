

CREATE TABLE `system_role_auth_scope` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
                                          `role_id` bigint DEFAULT NULL COMMENT '角色',
                                          `auth_role_id` bigint DEFAULT NULL COMMENT '授权角色ID',
                                          `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                          `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                          `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                          `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色授权范围表';

CREATE TABLE `system_role_dept` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
                                    `role_id` bigint NOT NULL COMMENT '角色id',
                                    `dept_id` bigint NOT NULL COMMENT '部门ID',
                                    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色部门权限表';

CREATE TABLE `system_role_region` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
                                      `role_id` bigint NOT NULL COMMENT '角色ID',
                                      `region_id` bigint NOT NULL COMMENT '区域id',
                                      `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色区域权限表';


CREATE TABLE `pw_duty_plan_question` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `duty_plan_id` bigint NOT NULL COMMENT '关联值班计划id',
                                         `question_answer` json NOT NULL COMMENT '问卷回答json',
                                         `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
                                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                         `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='值班计划问卷回答表';

ALTER TABLE `pw_duty_record`
    ADD COLUMN `clock_type` BIGINT COMMENT '0签到  1签退';
ALTER TABLE `pw_duty_record`
    ADD COLUMN `image_path` VARCHAR(512) COMMENT '打卡图片地址';

ALTER TABLE `system_users`
    ADD COLUMN `user_info_json` json COMMENT '港中大数据同步json';
ALTER TABLE `system_users`
    ADD COLUMN `college_zh` VARCHAR(128) COMMENT '港中大中文院校';
ALTER TABLE `system_users`
    ADD COLUMN `org_index_code` VARCHAR(128) COMMENT '港中大部门code';
ALTER TABLE `system_users`
    ADD COLUMN `specialty` VARCHAR(128) COMMENT '专业';

ALTER TABLE `system_dept`
    ADD COLUMN `parent_code` VARCHAR(128) COMMENT '父code';