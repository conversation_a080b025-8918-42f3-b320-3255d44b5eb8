ALTER TABLE `system_users`
ADD COLUMN `affiliation_dept_id` BIGINT COMMENT '所属部门id';

ALTER TABLE `system_users`
ADD COLUMN `card_number` VARCHAR(25) COMMENT '卡号';

ALTER TABLE `system_users`
ADD COLUMN `region_id` BIGINT COMMENT '区域id';

ALTER TABLE `system_users`
ADD COLUMN `face_image` VARCHAR(512) COMMENT '人脸';

ALTER TABLE `system_dept`
ADD COLUMN `code` VARCHAR(30) COMMENT '标识';

ALTER TABLE `system_dept`
ADD COLUMN `remark` VARCHAR(500) COMMENT '备注';

ALTER TABLE `pw_region`
ADD COLUMN `remark` VARCHAR(500) COMMENT '备注';

INSERT INTO
    `system_dict_type` (
        `id`,
        `name`,
        `type`,
        `status`,
        `remark`,
        `creator`,
        `create_time`,
        `updater`,
        `update_time`,
        `deleted`,
        `deleted_time`
    )
VALUES (
        654,
        '账号状态',
        'system_user_status',
        0,
        '',
        '1',
        '2025-04-19 23:47:37',
        '1',
        '2025-04-19 23:47:37',
        b'0',
        '1970-01-01 00:00:00'
    );

INSERT INTO
    `system_dict_data` (
        `id`,
        `sort`,
        `label`,
        `value`,
        `dict_type`,
        `status`,
        `color_type`,
        `css_class`,
        `remark`,
        `creator`,
        `create_time`,
        `updater`,
        `update_time`,
        `deleted`
    )
VALUES (
        1709,
        1,
        '正常',
        '0',
        'system_user_status',
        0,
        'success',
        '',
        '',
        '1',
        '2025-04-19 23:49:05',
        '1',
        '2025-04-19 23:49:05',
        b'0'
    );

INSERT INTO
    `system_dict_data` (
        `id`,
        `sort`,
        `label`,
        `value`,
        `dict_type`,
        `status`,
        `color_type`,
        `css_class`,
        `remark`,
        `creator`,
        `create_time`,
        `updater`,
        `update_time`,
        `deleted`
    )
VALUES (
        1710,
        2,
        '停用',
        '1',
        'system_user_status',
        0,
        'default',
        '',
        '',
        '1',
        '2025-04-19 23:49:42',
        '1',
        '2025-04-19 23:49:42',
        b'0'
    );

-- 2025 04/30新增
CREATE TABLE `pw_user_group` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(255) NOT NULL COMMENT '名称',
     `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 16 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '人员组';

CREATE TABLE `pw_user_group_rel` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `group_id` bigint NOT NULL COMMENT '组id',
    `user_id` bigint NOT NULL COMMENT '人员ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 16 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分组人员关系';

CREATE TABLE `pw_point_group` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `name` varchar(255) NOT NULL COMMENT '名称',
     `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 16 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '点位组';

CREATE TABLE `pw_point_group_rel` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
    `group_id` bigint NOT NULL COMMENT '组id',
    `point_id` bigint NOT NULL COMMENT '点位ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 16 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分组点位关系';

INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3030, 0, '分组管理', 0, '', 2, 0, 2934, 'group', 'ep:add-location', 'pw/group/index', 'Group', 1, 1, 1, '1', '2025-04-29 15:01:49', '1', '2025-04-29 15:01:49');

INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3031, 0, '门禁组查询', 0, 'pw:point-group:query', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:05:48', '1', '2025-04-29 16:10:44');
INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3032, 0, '门禁组创建', 0, 'pw:point-group:create', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:06:13', '1', '2025-04-29 16:11:13');
INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3033, 0, '门禁组更新', 0, 'pw:point-group:update', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:06:31', '1', '2025-04-29 16:11:20');
INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3034, 0, '门禁组删除', 0, 'pw:point-group:delete', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:07:15', '1', '2025-04-29 16:11:28');
INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3035, 0, '人员组查询', 0, 'pw:user-group:query', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:07:40', '1', '2025-04-29 16:11:38');
INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3036, 0, '人员组创建', 0, 'pw:user-group:create', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:07:58', '1', '2025-04-29 16:11:42');
INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3037, 0, '人员组更新', 0, 'pw:user-group:update', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:08:15', '1', '2025-04-29 16:11:46');
INSERT INTO system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3038, 0, '人员组删除', 0, 'pw:user-group:delete', 3, 0, 3030, '', '', '', '', 1, 1, 1, '1', '2025-04-29 16:08:40', '1', '2025-04-29 16:11:50');


-- 2025 05/01新增人员权限
CREATE TABLE `pw_point_permission` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户id或者用户组id',
    `user_type` TINYINT NOT NULL DEFAULT '0' COMMENT '0:用户id 1:用户组id',
    `point_id` bigint NOT NULL DEFAULT '0' COMMENT '门禁点id或者门禁点组id',
    `point_type` TINYINT NOT NULL DEFAULT '0' COMMENT '0:门禁点id 1:门禁点组id',
    `expired` TINYINT NOT NULL DEFAULT '0' COMMENT '0：长期有效；1：自定义有效期',
    `expired_begin` datetime COMMENT '创建时间',
    `expired_end` datetime COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 16 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '人员点位权限配置';

INSERT INTO cloud.system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3028, 0, '人员权限', 0, '', 2, 0, 2934, 'perm-user', 'fa:user', 'pw/permissionuser/index', 'PermissionUser', 1, 1, 1, '144', '2025-04-28 21:49:08', '144', '2025-04-28 21:49:08');

INSERT INTO cloud.system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3039, 0, '权限创建', 0, 'pw:user-point:create', 3, 0, 3028, '', '', '', '', 1, 1, 1, '1', '2025-05-08 12:05:57', '1', '2025-05-08 12:05:57');
INSERT INTO cloud.system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3040, 0, '权限删除', 0, 'pw:user-point:delete', 3, 0, 3028, '', '', '', '', 1, 1, 1, '1', '2025-05-08 12:08:05', '1', '2025-05-08 12:08:05');
INSERT INTO cloud.system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3041, 0, '权限查询', 0, 'pw:user-point:delete', 3, 0, 3028, '', '', '', '', 1, 1, 1, '1', '2025-05-08 12:08:24', '1', '2025-05-08 12:08:24');

INSERT INTO cloud.system_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3029, 0, '门禁点权限', 0, '', 2, 0, 2934, 'perm-point', 'fa:bitbucket', 'pw/permissionpoint/index', 'PermissionPoint', 1, 1, 1, '144', '2025-04-28 21:52:23', '144', '2025-04-28 21:52:23');



-- 2025/05/12
CREATE TABLE `pw_borrow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `apply_id` bigint NOT NULL COMMENT '借用人id',
  `tel` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `apply_time` datetime DEFAULT NULL COMMENT '申请日期',
  `expected_borrow_time` datetime DEFAULT NULL COMMENT '预计借用日期',
  `actual_borrow_time` datetime DEFAULT NULL COMMENT '实际借用日期',
  `expected_return_time` datetime DEFAULT NULL COMMENT '预计归还日期',
  `actual_return_time` datetime DEFAULT NULL COMMENT '实际归还日期',
  `apply_status` tinyint NOT NULL DEFAULT '0' COMMENT '0：待审批；10：审批通过 20:待归还 30:超时待归还 40:归还待确认 90:确认完成',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物资借用表单';

CREATE TABLE `pw_borrow_goods` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `borrow_id` bigint NOT NULL COMMENT '物资借用表单id',
  `goods_id` bigint NOT NULL COMMENT '物资id',
  `borrow_num` bigint NOT NULL COMMENT '借用数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='借用物资';

CREATE TABLE `pw_borrow_image` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `borrow_id` bigint NOT NULL COMMENT '物资借用表单id',
  `apply_status` tinyint NOT NULL DEFAULT '0' COMMENT '0：待审批；10：审批通过 20:待归还 30:超时待归还 40:归还待确认 90:确认完成',
  `image` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物资借用流程图片';

CREATE TABLE `pw_borrow_process` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `borrow_id` bigint NOT NULL COMMENT '物资借用表单id',
  `apply_status` tinyint NOT NULL DEFAULT '0' COMMENT '0：待审批；10：审批通过 20:待归还 30:超时待归还 40:归还待确认 90:确认完成',
  `process_id` bigint NOT NULL COMMENT '处理人id',
  `process_time` datetime DEFAULT NULL COMMENT '处理日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物资借用流程节点';

DROP TABLE `pw_goods`;
CREATE TABLE `pw_goods` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '物品ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `tenant_id` bigint DEFAULT '0' COMMENT '租户编号',
  `cover` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '物品封面',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物品名称',
  `goods_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '物品简介',
  `amount` int DEFAULT '0' COMMENT '总库存数量',
  `unit` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物品单位',
  `status` tinyint(1) DEFAULT '0' COMMENT '物品状态',
  `mount4borrow` int DEFAULT '0' COMMENT '已借出',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物品管理';



CREATE TABLE `system_app_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '菜单状态',
  `permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '权限标识',
  `type` tinyint NOT NULL COMMENT '菜单类型',
  `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父菜单ID',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '#' COMMENT '菜单图标',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `component_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件名',
  `visible` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可见',
  `keep_alive` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否缓存',
  `always_show` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否总是显示',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3051 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP菜单权限表';


CREATE TABLE `system_role_app_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6308 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';

INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(1, 0, '房间预约', 0, '', 1, 0, 0, '', '', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:01', '1', '2025-05-21 17:23:01');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(2, 0, '房间预约', 0, '', 2, 0, 1, '/pages/meeting/reservation/index', 'meeting-reservation', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:01', '1', '2025-05-21 17:23:01');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(3, 0, '我的预约', 0, '', 2, 0, 1, '/pages/meeting/myRecord/index', 'goods-records', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:01', '1', '2025-05-21 17:23:01');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(4, 0, '待我处理', 0, '', 2, 0, 1, '/pages/meeting/myHandle/index', 'my-handle', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 17:23:02');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(5, 0, '所有预约', 0, '', 2, 0, 1, '/pages/meeting/allRecord/index', 'my-handle', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 17:23:02');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(6, 0, '物资借用', 0, '', 1, 0, 0, '', '', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 17:23:02');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(7, 0, '物资借用', 0, '', 2, 0, 6, '/pages/goods/borrow/index', 'goods-borrow', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 10:00:38');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(8, 0, '我的借用', 0, '', 2, 0, 6, '/pages/goods/myApprove/index', 'goods-records', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 10:00:38');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(9, 0, '待我处理', 0, '', 2, 0, 6, '/pages/goods/myHandle/index', 'my-handle', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 09:59:30');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(10, 0, '所有借用', 0, '', 2, 0, 6, '/pages/goods/records/index', 'goods-records', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 09:59:30');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(11, 0, '值班', 0, '', 1, 0, 0, '', '', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 17:23:02');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(12, 0, '今日签到', 0, '', 2, 0, 11, '/pages/duty/signToday/index', 'goods-borrow', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 09:59:30');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(13, 0, '排班表', 0, '', 2, 0, 11, '/pages/duty/schedule/index', 'goods-records', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 09:59:30');
INSERT INTO cloud.system_app_menu
(id, deleted, name, status, permission, `type`, sort, parent_id, `path`, icon, component, component_name, visible, keep_alive, always_show, creator, create_time, updater, update_time)
VALUES(14, 0, '签到记录', 0, '', 2, 0, 11, '/pages/duty/signRecord/index', 'my-handle', NULL, NULL, 1, 1, 1, '1', '2025-05-21 17:23:02', '1', '2025-05-21 09:59:30');

INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(1, 1, 1, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(2, 1, 2, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(3, 1, 3, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(4, 1, 4, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(5, 1, 5, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(6, 1, 6, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(7, 1, 7, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(8, 1, 8, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(9, 1, 9, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(10, 1, 10, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(11, 1, 11, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(12, 1, 12, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(13, 1, 13, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(14, 1, 14, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(15, 2, 1, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(16, 2, 2, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(17, 2, 3, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(18, 2, 4, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(19, 2, 5, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(20, 2, 6, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(21, 2, 7, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(22, 2, 8, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(23, 2, 9, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(24, 2, 10, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(25, 2, 11, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(26, 2, 12, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(27, 2, 13, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(28, 2, 14, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(29, 155, 1, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(30, 155, 2, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(31, 155, 3, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(32, 155, 4, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(33, 155, 6, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(34, 155, 7, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(35, 155, 8, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(36, 155, 9, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(37, 155, 11, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(38, 155, 12, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(39, 155, 13, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);
INSERT INTO cloud.system_role_app_menu
(id, role_id, menu_id, creator, create_time, updater, update_time, deleted, tenant_id)
VALUES(40, 155, 14, '1', '2025-05-21 17:38:01', '1', '2025-05-21 17:38:01', 0, 0);


CREATE TABLE `pw_point_rule` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
    `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `user_id` bigint NOT NULL DEFAULT '0' COMMENT '部门id或者用户组id',
    `user_type` TINYINT NOT NULL DEFAULT '0' COMMENT '0:部门id 1:用户组id',
    `point_id` bigint NOT NULL DEFAULT '0' COMMENT '区域id或者门禁点组id',
    `point_type` TINYINT NOT NULL DEFAULT '0' COMMENT '0:区域id 1:门禁点组id',
    `expired` TINYINT NOT NULL DEFAULT '0' COMMENT '0：长期有效；1：自定义有效期',
    `expired_begin` datetime COMMENT '创建时间',
    `expired_end` datetime COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 16 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '点位自动规则';



CREATE TABLE IF NOT EXISTS `dept_sync_info` (
  `dept_id` bigint NOT NULL DEFAULT '0' COMMENT '用户id',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `info_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户信息hash',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门同步信息表';


CREATE TABLE IF NOT EXISTS `user_sync_info` (
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户id',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `info_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户信息hash',
  `card_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '卡号hash',
  `face_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '人脸hash',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户同步信息表';

ALTER TABLE `pw_callback_log`
	ADD COLUMN `channel_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '通道名称' ;